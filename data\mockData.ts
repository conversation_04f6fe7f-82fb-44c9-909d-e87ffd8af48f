// Mock data for the application

// User data
export const userData = {
  userName: 'dzefzed JALALezed',
  totalDonations: '120,811 Dhs',
};

// Recent donations data
export const recentDonations = [
  {
    date: 'Apr 8, 2025',
    amount: '20000 Dhs',
    status: 'Entrée',
    service: 'Non Identifié',
    type: 'Financière',
    channel: 'Virement',
  },
  {
    date: 'Apr 8, 2025',
    amount: '2500 Dhs',
    status: 'Entrée',
    service: 'Kafalat Handicapé',
    type: 'Financière',
    channel: 'Virement',
  },
  {
    date: 'Mar 24, 2025',
    amount: '500 Dhs',
    status: 'Entrée',
    service: 'Non Identifié',
    type: 'Financière',
    channel: 'Virement',
  },
];

// Dashboard stats data
export const dashboardStats = [
  {
    title: 'Total Donations',
    value: '120,811 Dhs',
    icon: 'attach-money' as const,
    color: '#4AC29A',
    bgColor: '#E6F9F2',
  },
  {
    title: 'Bénéficiaires',
    value: '156',
    icon: 'people' as const,
    color: '#4A7FC2',
    bgColor: '#E6EFF9',
  },
  {
    title: 'Donateurs',
    value: '89',
    icon: 'volunteer-activism' as const,
    color: '#C24A9D',
    bgColor: '#F9E6F6',
  },
  {
    title: 'Actions',
    value: '243',
    icon: 'event' as const,
    color: '#C2994A',
    bgColor: '#F9F6E6',
  },
];

// Beneficiaries data
export const BENEFICIARIES = [
  {
    id: '1',
    name: 'Ahmed Khalid',
    status: 'Active',
    category: 'Orphan',
    location: 'Casablanca',
    lastDonation: '15 Apr 2025',
    phone: '+212 612 345 678',
    email: '<EMAIL>',
    address: '123 Hassan II Boulevard, Casablanca',
    joinDate: '10 Jan 2023',
    bio: 'Ahmed is a 12-year-old orphan who lives with his grandmother. He is currently in primary school and shows great interest in mathematics and science.',
    coordinates: {
      latitude: 33.5731,
      longitude: -7.5898
    },
    donationHistory: [
      { date: '15 Apr 2025', amount: '1,500 Dhs', type: 'Monthly Support' },
      { date: '15 Mar 2025', amount: '1,500 Dhs', type: 'Monthly Support' },
      { date: '15 Feb 2025', amount: '1,500 Dhs', type: 'Monthly Support' },
      { date: '20 Jan 2025', amount: '2,000 Dhs', type: 'School Supplies' },
    ]
  },
  {
    id: '2',
    name: 'Fatima Zahra',
    status: 'Active',
    category: 'Elderly',
    location: 'Rabat',
    lastDonation: '10 Apr 2025',
    phone: '+212 661 987 654',
    email: '<EMAIL>',
    address: '45 Mohammed V Avenue, Rabat',
    joinDate: '05 Mar 2022',
    bio: 'Fatima is a 72-year-old widow who lives alone. She has health issues that require regular medical attention and support for daily activities.',
    coordinates: {
      latitude: 34.0209,
      longitude: -6.8416
    },
    donationHistory: [
      { date: '10 Apr 2025', amount: '2,000 Dhs', type: 'Medical Support' },
      { date: '15 Mar 2025', amount: '1,200 Dhs', type: 'Monthly Support' },
      { date: '20 Feb 2025', amount: '3,000 Dhs', type: 'Medical Treatment' },
      { date: '15 Jan 2025', amount: '1,200 Dhs', type: 'Monthly Support' },
    ]
  },
  {
    id: '3',
    name: 'Omar Benali',
    status: 'Inactive',
    category: 'Disabled',
    location: 'Lisbon',
    lastDonation: '28 Mar 2025',
    phone: '+212 633 456 789',
    email: '<EMAIL>',
    address: '78 Gueliz District, Marrakech',
    joinDate: '15 Jun 2023',
    bio: 'Omar is a 35-year-old man with physical disabilities. He was previously employed but lost his job due to health complications. He needs support for medical expenses and daily living.',
    donationHistory: [
      { date: '28 Mar 2025', amount: '3,500 Dhs', type: 'Medical Equipment' },
      { date: '15 Feb 2025', amount: '1,800 Dhs', type: 'Monthly Support' },
      { date: '15 Jan 2025', amount: '1,800 Dhs', type: 'Monthly Support' },
      { date: '20 Dec 2024', amount: '2,500 Dhs', type: 'Therapy Sessions' },
    ]
  },
  {
    id: '4',
    name: 'Nadia Tazi',
    status: 'Active',
    category: 'Widow',
    location: 'Tangier',
    lastDonation: '05 Apr 2025',
    phone: '+212 678 123 456',
    email: '<EMAIL>',
    address: '32 Ibn Battuta Street, Tangier',
    joinDate: '20 Sep 2022',
    bio: 'Nadia is a 42-year-old widow with three children. She works part-time but struggles to cover all expenses for her family, especially education costs for her children.',
    // No coordinates for this beneficiary
    donationHistory: [
      { date: '05 Apr 2025', amount: '2,500 Dhs', type: 'Education Support' },
      { date: '10 Mar 2025', amount: '1,500 Dhs', type: 'Monthly Support' },
      { date: '15 Feb 2025', amount: '1,500 Dhs', type: 'Monthly Support' },
      { date: '20 Jan 2025', amount: '3,000 Dhs', type: 'School Fees' },
    ]
  },
  {
    id: '5',
    name: 'Karim Alaoui',
    status: 'Pending',
    category: 'Student',
    location: 'Fez',
    lastDonation: '20 Mar 2025',
    phone: '+212 645 789 123',
    email: '<EMAIL>',
    address: '56 University District, Fez',
    joinDate: '01 Feb 2024',
    bio: 'Karim is a 19-year-old university student from a low-income family. He shows exceptional academic performance but needs financial support to continue his education.',
    coordinates: {
      latitude: 34.0181,
      longitude: -5.0078
    },
    donationHistory: [
      { date: '20 Mar 2025', amount: '4,000 Dhs', type: 'Tuition Fees' },
      { date: '15 Feb 2025', amount: '1,000 Dhs', type: 'Books and Materials' },
      { date: '10 Jan 2025', amount: '1,200 Dhs', type: 'Monthly Support' },
      { date: '05 Dec 2024', amount: '1,200 Dhs', type: 'Monthly Support' },
    ]
  },
];
