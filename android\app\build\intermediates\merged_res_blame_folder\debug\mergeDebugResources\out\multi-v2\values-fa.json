{"logs": [{"outputFile": "com.abdoxm.assistantapp-mergeDebugResources-64:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\582d0c2f5e6292ce32dc808d4c3bbb90\\transformed\\browser-1.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6819,7129,7226,7337", "endColumns": "98,96,110,102", "endOffsets": "6913,7221,7332,7435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25dd2e9a2ec645a6ceb46052888a9d56\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,12983", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,13060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,273,342,423,491,557,631,706,787,868,937,1016,1094,1168,1250,1331,1410,1483,1554,1642,1713,1789,1861", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,196,268,337,418,486,552,626,701,782,863,932,1011,1089,1163,1245,1326,1405,1478,1549,1637,1708,1784,1856,1932"}, "to": {"startLines": "33,49,75,77,78,80,94,95,96,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,4516,7440,7581,7650,7791,8826,8892,8966,12673,12754,12835,12904,13302,13380,13454,13536,13617,13696,13769,13840,14029,14100,14176,14248", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "3063,4588,7507,7645,7726,7854,8887,8961,9036,12749,12830,12899,12978,13375,13449,13531,13612,13691,13764,13835,13923,14095,14171,14243,14319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\429888db8e5916503c7ec8b30991ae80\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3068,3143,3220,3302,3395,4206,4303,4432,6918,6976,7039,7512,7731,7859,7950,8013,8077,8136,8203,8265,8320,8443,8501,8562,8617,8689,9041,9122,9202,9299,9380,9462,9592,9666,9740,9872,9958,10035,10086,10140,10206,10277,10354,10425,10504,10577,10651,10721,10795,10896,10982,11056,11145,11237,11311,11384,11473,11524,11604,11671,11754,11838,11900,11964,12027,12096,12190,12291,12384,12482,12537,12595,13065,13151,13228", "endLines": "5,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "304,3138,3215,3297,3390,3477,4298,4427,4511,6971,7034,7124,7576,7786,7945,8008,8072,8131,8198,8260,8315,8438,8496,8557,8612,8684,8821,9117,9197,9294,9375,9457,9587,9661,9735,9867,9953,10030,10081,10135,10201,10272,10349,10420,10499,10572,10646,10716,10790,10891,10977,11051,11140,11232,11306,11379,11468,11519,11599,11666,11749,11833,11895,11959,12022,12091,12185,12286,12379,12477,12532,12590,12668,13146,13223,13297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\480ce62020c830cf93653ad6a17bbf11\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3482,3581,3683,3782,3882,3983,4089,13928", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3576,3678,3777,3877,3978,4084,4201,14024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0964178eb307674e8f00b6340d381c7e\\transformed\\play-services-base-18.2.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4593,4701,4858,4987,5090,5230,5356,5470,5727,5880,5990,6160,6291,6444,6600,6666,6734", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "4696,4853,4982,5085,5225,5351,5465,5567,5875,5985,6155,6286,6439,6595,6661,6729,6814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6cc7e96abcd1f2665995aea30d859a4\\transformed\\play-services-basement-18.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5572", "endColumns": "154", "endOffsets": "5722"}}]}]}