import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiClient } from './apiClient';

// Define types for the API responses
export interface LanguageCommunication {
  id: number;
  code: string;
  name: string;
  nameAr: string;
  nameEn: string;
}

export interface SchoolLevel {
  id: number | null;
  code: string | null;
  name: string | null;
  nameAr: string | null;
  nameEn: string | null;
  type: string | null;
  links: any[];
}

export interface Zone {
  id: number;
  code: string;
  name: string;
  nameAr: string;
}

export interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  rolePrivileges: any | null;
}

export interface Profile {
  id: number;
  nameProfile: string;
  moduleFunctionalities: {
    [key: string]: string[];
  };
}

export interface CacheAdUser {
  id: number;
  assistantId: number | null;
  zoneId: number | null;
  azureDirectoryId: string;
  firstName: string;
  lastName: string;
  mail: string;
  creationDate: string;
  updateDate: string | null;
  profile: Profile;
  role: Role;
  lastLoginInDate: string | null;
}

export interface Assistant {
  id: number;
  code: string;
  cityId: number | null;
  address: string | null;
  creationDate: string | null;
  updateDate: string | null;
  zoneId: number;
  cacheAdUser: CacheAdUser;
  cacheAdUserId: number;
  zone: Zone;
  hasBeneficiaries: boolean;
  dateAffectationToZone: string | null;
  dateEndAffectationToZone: string | null;
  firstName: string;
  lastName: string;
  email: string;
  birthDate: string | null;
  languageCommunicationIds: number[];
  languageCommunicationDetails: LanguageCommunication[] | null;
  schoolLevel: SchoolLevel;
  status: boolean;
  pictureUrl: string | null;
  picture64: string | null;
  cinNumber: string | null;
  phone: string | null;
  oldAssistant: boolean;
  password: string | null;
  additionalInfo: string | null;
}

/**
 * Fetches assistant data by email
 * @param email The email of the assistant to fetch
 * @returns The assistant data or null if not found
 */
export const getAssistantByEmail = async (email: string): Promise<Assistant | null> => {
  try {
    // Use the ApiClient to make the request
    const data = await ApiClient.get<Assistant>('/assistants/by-email', { email });

    // Store the assistant data for future use
    await AsyncStorage.setItem('assistant_data', JSON.stringify(data));

    return data;
  } catch (error) {
    console.error('Error fetching assistant data:', error);
    return null;
  }
};

/**
 * Gets the stored assistant data from AsyncStorage
 * @returns The stored assistant data or null if not found
 */
export const getStoredAssistantData = async (): Promise<Assistant | null> => {
  try {
    const data = await AsyncStorage.getItem('assistant_data');
    if (data) {
      return JSON.parse(data) as Assistant;
    }
    return null;
  } catch (error) {
    console.error('Error getting stored assistant data:', error);
    return null;
  }
};

/**
 * Fetches the current user's assistant data
 * @returns The assistant data for the current user or null if not found
 */
export const getCurrentUserAssistantData = async (): Promise<Assistant | null> => {
  try {
    // First try to get from storage
    const storedData = await getStoredAssistantData();
    if (storedData) {
      return storedData;
    }

    // If not in storage, get the user's email from auth context
    const userData = await AsyncStorage.getItem('user_data');
    if (!userData) {
      console.error('No user data available');
      return null;
    }

    const user = JSON.parse(userData);
    if (!user || !user.email) {
      console.error('User email not available');
      return null;
    }

    // Fetch the assistant data by email
    return await getAssistantByEmail(user.email);
  } catch (error) {
    console.error('Error getting current user assistant data:', error);
    return null;
  }
};
