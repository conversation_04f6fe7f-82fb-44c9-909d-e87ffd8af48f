# API Module

This module provides a structured way to interact with the backend API.

## Usage Examples

### Using the Assistant API

```tsx
import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useAssistant } from '@/api/general';

const AssistantProfile = () => {
  const { assistant, loading, error } = useAssistant();

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Error: {error}</Text>
      </View>
    );
  }

  if (!assistant) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>No assistant data found</Text>
      </View>
    );
  }

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold' }}>
        {assistant.firstName} {assistant.lastName}
      </Text>
      <Text style={{ fontSize: 16, marginTop: 10 }}>
        Email: {assistant.email}
      </Text>
      <Text style={{ fontSize: 16, marginTop: 5 }}>
        Code: {assistant.code}
      </Text>
      <Text style={{ fontSize: 16, marginTop: 5 }}>
        Zone: {assistant.zone.name}
      </Text>
      <Text style={{ fontSize: 16, marginTop: 5 }}>
        Role: {assistant.cacheAdUser.role.name}
      </Text>
    </View>
  );
};

export default AssistantProfile;
```

### Using the API Client Directly

```tsx
import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { ApiClient, Assistant } from '@/api/general';

const AssistantDetails = ({ email }: { email: string }) => {
  const [assistant, setAssistant] = useState<Assistant | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssistant = async () => {
      try {
        setLoading(true);
        const data = await ApiClient.get<Assistant>('/assistants/by-email', { email });
        setAssistant(data);
      } catch (err) {
        console.error('Error fetching assistant:', err);
        setError('Failed to load assistant data');
      } finally {
        setLoading(false);
      }
    };

    fetchAssistant();
  }, [email]);

  if (loading) {
    return <ActivityIndicator size="large" color="#0000ff" />;
  }

  if (error) {
    return <Text>Error: {error}</Text>;
  }

  if (!assistant) {
    return <Text>No assistant found</Text>;
  }

  return (
    <View>
      <Text>Name: {assistant.firstName} {assistant.lastName}</Text>
      <Text>Email: {assistant.email}</Text>
    </View>
  );
};

export default AssistantDetails;
```
