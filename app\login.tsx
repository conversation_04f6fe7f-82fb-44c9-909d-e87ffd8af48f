import { Stack, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../api/msalConfig/AuthContext';
import { EmailPasswordLoginForm } from '../components/EmailPasswordLoginForm';

export default function LoginScreen() {
  const { signIn, error, isLoading, isAuthenticated, clearStorage } = useAuth();
  const [localLoading, setLocalLoading] = useState(false);
  const [showEmailPasswordForm, setShowEmailPasswordForm] = useState(true); // Default to email/password form
  const router = useRouter();

  // Auto-focus this screen on app load
  useEffect(() => {
    console.log('Login screen mounted');
  }, []);

  // Redirect to home if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, router]);

  const handleSignIn = async () => {
    try {
      setLocalLoading(true);
      await signIn();
    } catch (err) {
      console.error('Login error:', err);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleClearStorage = async () => {
    try {
      setLocalLoading(true);
      await clearStorage();
      console.log('Storage cleared successfully');
    } catch (err) {
      console.error('Error clearing storage:', err);
    } finally {
      setLocalLoading(false);
    }
  };

  const images = {
    someImage: require('../assets/images/login-header.jpg'),
    appLogo: require('../assets/images/applogo.png'),
    msLogo: require('../assets/images/microsoft-logo.png'),
   // more images
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />

        {/* Content section - Not scrollable */}
        <View style={styles.contentContainer}>
          {/* Logo */}
          <View>
              <Image source={images.appLogo} style={styles.logoPlaceholder}/>
          </View>

          {/* Welcome text */}
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeText}>Bonjour !</Text>
            <Text style={styles.subtitleText}>
              Veuillez vous connecter pour continuer.
            </Text>
          </View>

          {/* Authentication options */}
          {showEmailPasswordForm ? (
            <>
              {/* Email/Password Form - Main login option */}
              <EmailPasswordLoginForm
                onLoginSuccess={() => router.replace('/(tabs)')}
              />

              {/* Toggle to Microsoft login - Secondary option */}
              <TouchableOpacity
                style={styles.toggleButton}
                onPress={() => setShowEmailPasswordForm(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.toggleButtonText}>Sign in with Microsoft instead</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              {/* Microsoft Sign in button - Secondary option */}
              <TouchableOpacity
                style={styles.signInButton}
                onPress={handleSignIn}
                disabled={isLoading || localLoading}
                activeOpacity={0.7}
              >
                {(isLoading || localLoading) ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <>
                    <View style={styles.msLogoContainer}>
                      <Image source={images.msLogo} style={styles.msLogoText}/>
                    </View>
                    <Text style={styles.signInButtonText}>Sign in with Microsoft</Text>
                  </>
                )}
              </TouchableOpacity>

              {/* Toggle to Email/Password login - Main option */}
              <TouchableOpacity
                style={[styles.toggleButton, styles.primaryToggleButton]}
                onPress={() => setShowEmailPasswordForm(true)}
                activeOpacity={0.7}
              >
                <Text style={[styles.toggleButtonText, styles.primaryToggleText]}>Sign in with Email and Password</Text>
              </TouchableOpacity>
            </>
          )}

          {/* Error message - Show for Microsoft login only */}
          {error && !showEmailPasswordForm && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {/* Clear Storage Button (for debugging) */}
          <TouchableOpacity
            style={styles.clearStorageButton}
            onPress={handleClearStorage}
            disabled={isLoading || localLoading}
            activeOpacity={0.7}
          >
            {(isLoading || localLoading) ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.clearStorageButtonText}>Clear Storage</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFF',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#F8FAFF',
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center', // Center content vertically
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
    backgroundColor: '#F8FAFF',
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoPlaceholder: {
    width: 100,
    height: 80,
    marginBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  welcomeContainer: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    color: '#333',
  },
  subtitleText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    lineHeight: 22,
  },
  signInButton: {
    width: '100%',
    padding: 16,
    borderRadius: 30,
    backgroundColor: '#000000',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  msLogoContainer: {
    width: 24,
    height: 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  msLogoText: {
    color: '#0078D4',
    fontSize: 12,
    width: 24,
    height: 24,
    fontWeight: 'bold',
  },
  signInButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  toggleButton: {
    marginTop: 16,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  toggleButtonText: {
    color: '#0078D4',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  primaryToggleButton: {
    backgroundColor: '#F0F8FF', // Light blue background
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0078D4',
    marginTop: 20,
  },
  primaryToggleText: {
    fontWeight: 'bold',
    textDecorationLine: 'none',
  },
  errorContainer: {
    marginTop: 20,
    padding: 10,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 8,
    width: '100%',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
  clearStorageButton: {
    marginTop: 20,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#FF5252', // Red color for warning
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
  },
  clearStorageButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
