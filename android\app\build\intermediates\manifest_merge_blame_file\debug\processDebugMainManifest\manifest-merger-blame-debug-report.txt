1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.abdoxm.assistantapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:3-75
11-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:4:3-64
14-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:4:20-62
15    <uses-permission
15-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:5:3-77
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:5:20-75
17        android:maxSdkVersion="32" />
17-->[BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:7:3-63
18-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:7:20-61
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:8:3-78
19-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:8:20-76
20
21    <queries>
21-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:9:3-15:13
22        <intent>
22-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:10:5-14:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:7-58
23-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:7-67
25-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
27-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
28        </intent>
29
30        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
30-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
30-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
31        <intent>
31-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:15:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:16:13-79
32-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:16:21-76
33        </intent>
34        <intent>
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
35
36            <!-- Required for opening tabs if targeting API 30 -->
37            <action android:name="android.support.customtabs.action.CustomTabsService" />
37-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
37-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
38        </intent> <!-- Needs to be explicitly declared on Android R+ -->
39        <package android:name="com.google.android.apps.maps" />
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
40    </queries>
41    <!--
42  Allows Glide to monitor connectivity status and restart failed requests if users go from a
43  a disconnected to a connected network state.
44    -->
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->[BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
45-->[BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:22-76
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
46-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
46-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
47    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
47-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
47-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
48    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
49    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
50
51    <uses-feature
51-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
52        android:glEsVersion="0x00020000"
52-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
53        android:required="true" />
53-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
54
55    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
55-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
55-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
56
57    <permission
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
58        android:name="com.abdoxm.assistantapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
59        android:protectionLevel="signature" />
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
60
61    <uses-permission android:name="com.abdoxm.assistantapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
62    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
63    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
64    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
65    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
66    <!-- for Samsung -->
67    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
68    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
69    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
70    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
71    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
72    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
73    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
74    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
75    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
76    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
77    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
78    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
79    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
80    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
81    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
82    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
83    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
84
85    <application
85-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:3-36:17
86        android:name="com.abdoxm.assistantapp.MainApplication"
86-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:16-47
87        android:allowBackup="true"
87-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:162-188
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
89        android:debuggable="true"
90        android:extractNativeLibs="false"
91        android:icon="@mipmap/ic_launcher"
91-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:81-115
92        android:label="@string/app_name"
92-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:48-80
93        android:roundIcon="@mipmap/ic_launcher_round"
93-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:116-161
94        android:supportsRtl="true"
94-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:221-247
95        android:theme="@style/AppTheme"
95-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:189-220
96        android:usesCleartextTraffic="true" >
96-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:18-53
97        <meta-data
97-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:5-118
98            android:name="com.google.firebase.messaging.default_notification_channel_id"
98-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:16-92
99            android:value="default" />
99-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:93-116
100        <meta-data
100-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:5-135
101            android:name="com.google.firebase.messaging.default_notification_icon"
101-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:16-86
102            android:resource="@drawable/notification_icon" />
102-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:87-133
103        <meta-data
103-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:5-132
104            android:name="expo.modules.notifications.default_notification_icon"
104-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:16-83
105            android:resource="@drawable/notification_icon" />
105-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:84-130
106        <meta-data
106-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:5-83
107            android:name="expo.modules.updates.ENABLED"
107-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:16-59
108            android:value="false" />
108-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:60-81
109        <meta-data
109-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:5-105
110            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
110-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:16-80
111            android:value="ALWAYS" />
111-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:81-103
112        <meta-data
112-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:5-99
113            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
113-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:16-79
114            android:value="0" />
114-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:80-97
115
116        <activity
116-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:5-35:16
117            android:name="com.abdoxm.assistantapp.MainActivity"
117-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:15-43
118            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
118-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:44-134
119            android:exported="true"
119-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:256-279
120            android:launchMode="singleTask"
120-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:135-166
121            android:screenOrientation="portrait"
121-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:280-316
122            android:theme="@style/Theme.App.SplashScreen"
122-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:210-255
123            android:windowSoftInputMode="adjustResize" >
123-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:167-209
124            <intent-filter>
124-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:24:7-27:23
125                <action android:name="android.intent.action.MAIN" />
125-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:25:9-60
125-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:25:17-58
126
127                <category android:name="android.intent.category.LAUNCHER" />
127-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:26:9-68
127-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:26:19-66
128            </intent-filter>
129            <intent-filter>
129-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:28:7-34:23
130                <action android:name="android.intent.action.VIEW" />
130-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:7-58
130-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:15-56
131
132                <category android:name="android.intent.category.DEFAULT" />
132-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:9-67
132-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:19-65
133                <category android:name="android.intent.category.BROWSABLE" />
133-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:7-67
133-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:17-65
134
135                <data android:scheme="assisstantapp" />
135-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
135-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
136                <data android:scheme="exp+assisstant-app" />
136-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
136-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
137            </intent-filter>
138        </activity>
139
140        <provider
140-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
141            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
141-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
142            android:authorities="com.abdoxm.assistantapp.fileprovider"
142-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
143            android:exported="false"
143-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
144            android:grantUriPermissions="true" >
144-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
145            <meta-data
145-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
146                android:name="android.support.FILE_PROVIDER_PATHS"
146-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
147                android:resource="@xml/file_provider_paths" />
147-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
148        </provider>
149
150        <activity
150-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
151            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
151-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
152            android:exported="true"
152-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
153            android:launchMode="singleTask"
153-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
154            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
154-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
155            <intent-filter>
155-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
156                <action android:name="android.intent.action.VIEW" />
156-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:7-58
156-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:15-56
157
158                <category android:name="android.intent.category.DEFAULT" />
158-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:9-67
158-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:19-65
159                <category android:name="android.intent.category.BROWSABLE" />
159-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:7-67
159-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:17-65
160
161                <data android:scheme="expo-dev-launcher" />
161-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
161-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
162            </intent-filter>
163        </activity>
164        <activity
164-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
165            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
165-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
166            android:screenOrientation="portrait"
166-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
167            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
167-->[:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
168        <activity
168-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
169            android:name="expo.modules.devmenu.DevMenuActivity"
169-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
170            android:exported="true"
170-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
171            android:launchMode="singleTask"
171-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
172            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
172-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
173            <intent-filter>
173-->[:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
174                <action android:name="android.intent.action.VIEW" />
174-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:7-58
174-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:15-56
175
176                <category android:name="android.intent.category.DEFAULT" />
176-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:9-67
176-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:19-65
177                <category android:name="android.intent.category.BROWSABLE" />
177-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:7-67
177-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:17-65
178
179                <data android:scheme="expo-dev-menu" />
179-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
179-->C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
180            </intent-filter>
181        </activity>
182
183        <meta-data
183-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
184            android:name="org.unimodules.core.AppLoader#react-native-headless"
184-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
185            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
185-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
186        <meta-data
186-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
187            android:name="com.facebook.soloader.enabled"
187-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
188            android:value="true" />
188-->[:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
189
190        <activity
190-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
191            android:name="com.facebook.react.devsupport.DevSettingsActivity"
191-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
192            android:exported="false" />
192-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
193
194        <service
194-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
195            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
195-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
196            android:exported="false" >
196-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
197            <intent-filter android:priority="-1" >
197-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
197-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
198                <action android:name="com.google.firebase.MESSAGING_EVENT" />
198-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
198-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
199            </intent-filter>
200        </service>
201
202        <receiver
202-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
203            android:name="expo.modules.notifications.service.NotificationsService"
203-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
204            android:enabled="true"
204-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
205            android:exported="false" >
205-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
206            <intent-filter android:priority="-1" >
206-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
206-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
207                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
207-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
207-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
208                <action android:name="android.intent.action.BOOT_COMPLETED" />
208-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
208-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
209                <action android:name="android.intent.action.REBOOT" />
209-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
209-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
210                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
210-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
210-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
211                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
211-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
211-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
212                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
212-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
212-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
213            </intent-filter>
214        </receiver>
215
216        <activity
216-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
217            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
217-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
218            android:excludeFromRecents="true"
218-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
219            android:exported="false"
219-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
220            android:launchMode="standard"
220-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
221            android:noHistory="true"
221-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
222            android:taskAffinity=""
222-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
223            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
223-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
224
225        <provider
225-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:21:9-30:20
226            android:name="expo.modules.filesystem.FileSystemFileProvider"
226-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:22:13-74
227            android:authorities="com.abdoxm.assistantapp.FileSystemFileProvider"
227-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:23:13-74
228            android:exported="false"
228-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:24:13-37
229            android:grantUriPermissions="true" >
229-->[host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:25:13-47
230            <meta-data
230-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
231                android:name="android.support.FILE_PROVIDER_PATHS"
231-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
232                android:resource="@xml/file_system_provider_paths" />
232-->[:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
233        </provider>
234
235        <service
235-->[host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:11:9-14:56
236            android:name="expo.modules.location.services.LocationTaskService"
236-->[host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:12:13-78
237            android:exported="false"
237-->[host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:13:13-37
238            android:foregroundServiceType="location" />
238-->[host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:14:13-53
239
240        <receiver
240-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
241            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
241-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
242            android:exported="true"
242-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
243            android:permission="com.google.android.c2dm.permission.SEND" >
243-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
244            <intent-filter>
244-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
245                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
245-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
245-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
246            </intent-filter>
247
248            <meta-data
248-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
249                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
249-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
250                android:value="true" />
250-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
251        </receiver>
252        <!--
253             FirebaseMessagingService performs security checks at runtime,
254             but set to not exported to explicitly avoid allowing another app to call it.
255        -->
256        <service
256-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
257            android:name="com.google.firebase.messaging.FirebaseMessagingService"
257-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
258            android:directBootAware="true"
258-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
259            android:exported="false" >
259-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
260            <intent-filter android:priority="-500" >
260-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
260-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
261                <action android:name="com.google.firebase.MESSAGING_EVENT" />
261-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
261-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
262            </intent-filter>
263        </service>
264        <service
264-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
265            android:name="com.google.firebase.components.ComponentDiscoveryService"
265-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
266            android:directBootAware="true"
266-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
267            android:exported="false" >
267-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
268            <meta-data
268-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
269                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
269-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
271            <meta-data
271-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
272                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
272-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
274            <meta-data
274-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
275                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
275-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
277            <meta-data
277-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
278                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
278-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
280            <meta-data
280-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
281                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
281-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
283            <meta-data
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
284                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
286            <meta-data
286-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
287                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
287-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
288                android:value="com.google.firebase.components.ComponentRegistrar" />
288-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
289        </service>
290
291        <provider
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
292            android:name="com.google.firebase.provider.FirebaseInitProvider"
292-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
293            android:authorities="com.abdoxm.assistantapp.firebaseinitprovider"
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
294            android:directBootAware="true"
294-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
295            android:exported="false"
295-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
296            android:initOrder="100" />
296-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
297
298        <meta-data
298-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
299            android:name="com.google.android.gms.version"
299-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
300            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
300-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
301        <uses-library
301-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
302            android:name="org.apache.http.legacy"
302-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
303            android:required="false" />
303-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
304
305        <activity
305-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
306            android:name="com.google.android.gms.common.api.GoogleApiActivity"
306-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
307            android:exported="false"
307-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
308            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
308-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
309
310        <meta-data
310-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
311            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
311-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
312            android:value="GlideModule" />
312-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
313
314        <provider
314-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
315            android:name="androidx.startup.InitializationProvider"
315-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
316            android:authorities="com.abdoxm.assistantapp.androidx-startup"
316-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
317            android:exported="false" >
317-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
318            <meta-data
318-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
319                android:name="androidx.work.WorkManagerInitializer"
319-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
320                android:value="androidx.startup" />
320-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
321            <meta-data
321-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
322                android:name="androidx.emoji2.text.EmojiCompatInitializer"
322-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
323                android:value="androidx.startup" />
323-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
324            <meta-data
324-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
325                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
325-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
326                android:value="androidx.startup" />
326-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
327            <meta-data
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
328                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
329                android:value="androidx.startup" />
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
330        </provider>
331
332        <service
332-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
333            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
333-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
335            android:enabled="@bool/enable_system_alarm_service_default"
335-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
337        <service
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
338            android:name="androidx.work.impl.background.systemjob.SystemJobService"
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
340            android:enabled="@bool/enable_system_job_service_default"
340-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
341            android:exported="true"
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
342            android:permission="android.permission.BIND_JOB_SERVICE" />
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
343        <service
343-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
344            android:name="androidx.work.impl.foreground.SystemForegroundService"
344-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
346            android:enabled="@bool/enable_system_foreground_service_default"
346-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
347            android:exported="false" />
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
348
349        <receiver
349-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
350            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
350-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
352            android:enabled="true"
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
353            android:exported="false" />
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
354        <receiver
354-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
355            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
356            android:directBootAware="false"
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
357            android:enabled="false"
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
358            android:exported="false" >
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
359            <intent-filter>
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
360                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
360-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
360-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
361                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
362            </intent-filter>
363        </receiver>
364        <receiver
364-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
365            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
366            android:directBootAware="false"
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
367            android:enabled="false"
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
368            android:exported="false" >
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
369            <intent-filter>
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
370                <action android:name="android.intent.action.BATTERY_OKAY" />
370-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
370-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
371                <action android:name="android.intent.action.BATTERY_LOW" />
371-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
371-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
372            </intent-filter>
373        </receiver>
374        <receiver
374-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
375            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
376            android:directBootAware="false"
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
377            android:enabled="false"
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
378            android:exported="false" >
378-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
379            <intent-filter>
379-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
380                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
381                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
382            </intent-filter>
383        </receiver>
384        <receiver
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
385            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
386            android:directBootAware="false"
386-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
387            android:enabled="false"
387-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
388            android:exported="false" >
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
389            <intent-filter>
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
390                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
390-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
390-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
394            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
396            android:enabled="false"
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
399                <action android:name="android.intent.action.BOOT_COMPLETED" />
399-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
399-->[:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
400                <action android:name="android.intent.action.TIME_SET" />
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
401                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
402            </intent-filter>
403        </receiver>
404        <receiver
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
405            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
407            android:enabled="@bool/enable_system_alarm_service_default"
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
408            android:exported="false" >
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
409            <intent-filter>
409-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
410                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
410-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
410-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
411            </intent-filter>
412        </receiver>
413        <receiver
413-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
414            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
414-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
415            android:directBootAware="false"
415-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
416            android:enabled="true"
416-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
417            android:exported="true"
417-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
418            android:permission="android.permission.DUMP" >
418-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
419            <intent-filter>
419-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
420                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
420-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
420-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
421            </intent-filter>
422        </receiver>
423        <receiver
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
424            android:name="androidx.profileinstaller.ProfileInstallReceiver"
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
425            android:directBootAware="false"
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
426            android:enabled="true"
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
427            android:exported="true"
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
428            android:permission="android.permission.DUMP" >
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
429            <intent-filter>
429-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
430                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
431            </intent-filter>
432            <intent-filter>
432-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
433                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
433-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
433-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
434            </intent-filter>
435            <intent-filter>
435-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
436                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
436-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
436-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
437            </intent-filter>
438            <intent-filter>
438-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
439                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
439-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
439-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
440            </intent-filter>
441        </receiver>
442
443        <service
443-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
444            android:name="androidx.room.MultiInstanceInvalidationService"
444-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
445            android:directBootAware="true"
445-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
446            android:exported="false" />
446-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
447        <service
447-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
448            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
448-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
449            android:exported="false" >
449-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
450            <meta-data
450-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
451                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
451-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
452                android:value="cct" />
452-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
453        </service>
454        <service
454-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
455            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
455-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
456            android:exported="false"
456-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
457            android:permission="android.permission.BIND_JOB_SERVICE" >
457-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
458        </service>
459
460        <receiver
460-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
461            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
461-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
462            android:exported="false" />
462-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
463    </application>
464
465</manifest>
