import React, { useRef, useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { useAuth } from '../api/msalConfig/AuthContext';

interface EmailPasswordLoginFormProps {
  onLoginSuccess?: () => void;
}

export const EmailPasswordLoginForm: React.FC<EmailPasswordLoginFormProps> = ({
  onLoginSuccess,
}) => {
  const { signInWithEmailPassword, error, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  // Create a reference to the password input
  const passwordInputRef = useRef<TextInput>(null);

  const validateForm = (): boolean => {
    // Basic email validation
    if (!email || !email.includes('@') || !email.includes('.')) {
      setLocalError('Please enter a valid email address');
      return false;
    }

    // Password validation
    if (!password || password.length < 6) {
      setLocalError('Password must be at least 6 characters');
      return false;
    }

    setLocalError(null);
    return true;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLocalLoading(true);

      // Call signInWithEmailPassword and get the success status
      const success = await signInWithEmailPassword(email, password);

      // Only redirect on success
      if (success && onLoginSuccess) {
        onLoginSuccess();
      } else if (!success) {
        // If not successful but no error was thrown, set a default error
        setLocalError('Email ou password incorrect');

        // Clear the password field for security
        setPassword('');

        // Focus the password field for better UX
        setTimeout(() => {
          passwordInputRef.current?.focus();
        }, 100);
      }
    } catch (err: any) {
      console.error('Login error:', err);

      // Set a user-friendly error message
      setLocalError('Email ou password incorrect');

      // Clear the password field for security
      setPassword('');

      // Focus the password field for better UX
      setTimeout(() => {
        passwordInputRef.current?.focus();
      }, 100);
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <View style={styles.container}>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Email</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your email"
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          autoComplete="email"
          editable={!isLoading && !localLoading}
          returnKeyType="next"
          onSubmitEditing={() => passwordInputRef.current?.focus()}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Password</Text>
        <TextInput
          ref={passwordInputRef}
          style={styles.input}
          placeholder="Enter your password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          autoCapitalize="none"
          autoComplete="password"
          editable={!isLoading && !localLoading}
          returnKeyType="done"
          onSubmitEditing={handleLogin}
        />
      </View>

      {(localError || error) && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{localError || error}</Text>
        </View>
      )}

      <TouchableOpacity
        style={styles.loginButton}
        onPress={handleLogin}
        disabled={isLoading || localLoading}
        activeOpacity={0.7} // Improve touch feedback
      >
        {(isLoading || localLoading) ? (
          <ActivityIndicator color="#FFFFFF" size="small" />
        ) : (
          <Text style={styles.loginButtonText}>Sign In</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
    color: '#333333',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  errorContainer: {
    marginBottom: 20,
    padding: 12,
    backgroundColor: '#FFEEEE',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FF9999',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'center',
  },
  loginButton: {
    height: 56, // Increased height for better touch target
    backgroundColor: '#0078D4', // Microsoft blue
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24, // Increased margin for better separation
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2, // Increased shadow opacity
    shadowRadius: 4,
    elevation: 4, // Increased elevation
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 18, // Increased font size
    fontWeight: 'bold',
    letterSpacing: 0.5, // Added letter spacing for better readability
  },
});
