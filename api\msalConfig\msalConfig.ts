import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Azure AD Configuration
export const msalConfig = {
  clientId: Constants.expoConfig?.extra?.EXPO_PUBLIC_AZURE_CLIENT_ID || '',
  tenantId: Constants.expoConfig?.extra?.EXPO_PUBLIC_AZURE_TENANT_ID || '',
  redirectUri: 'msalb9211a3b-894b-428f-b4bb-20a09af8eed3://auth',
  authority: Constants.expoConfig?.extra?.EXPO_PUBLIC_AZURE_AUTHORITY || '',
  scopes: (Constants.expoConfig?.extra?.EXPO_PUBLIC_AZURE_SCOPES || 'openid profile email User.Read').split(' '),
};

// Configuration for web platform
export const msalConfigWeb = {
  auth: {
    clientId: msalConfig.clientId,
    authority: msalConfig.authority,
    redirectUri: Platform.OS === 'web' ? window.location.origin : msalConfig.redirectUri,
    postLogoutRedirectUri: Platform.OS === 'web' ? window.location.origin : msalConfig.redirectUri,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
};

// Configuration for mobile platform
export const msalConfigMobile = {
  auth: {
    clientId: msalConfig.clientId,
    authority: msalConfig.authority,
    redirectUri: msalConfig.redirectUri,
  },
  scopes: msalConfig.scopes,
};

// Login request object
export const loginRequest = {
  scopes: msalConfig.scopes,
};
