import { useAssistant } from '@/api/general';
import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useState } from 'react';
import { ActivityIndicator, Alert, Image, Platform, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../api/msalConfig/AuthContext';

export default function ProfileScreen() {
  // Get assistant data from API
  const { assistant, loading, error, refreshAssistantData } = useAssistant();
  const { user, signOut, getAccessToken, refreshToken } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [tokenLoading, setTokenLoading] = useState(false);



  // Function to refresh the access token
  const handleRefreshToken = async () => {
    try {
      setTokenLoading(true);
      const success = await refreshToken();
      if (success) {
        const token = await getAccessToken();
        setAccessToken(token);
        Alert.alert('Success', 'Token refreshed successfully');
      } else {
        Alert.alert('Error', 'Failed to refresh token');
      }
    } catch (err) {
      console.error('Error refreshing token:', err);
      Alert.alert('Error', 'Failed to refresh token');
    } finally {
      setTokenLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await refreshAssistantData();
      console.log('assistant', assistant);

      Alert.alert('Success', 'Profile data refreshed');
    } catch (err) {
      console.error('Error refreshing profile data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      Alert.alert(
        'Sign Out',
        'Are you sure you want to sign out?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Sign Out',
            style: 'destructive',
            onPress: async () => {
              await signOut();
            }
          },
        ]
      );
    } catch (err) {
      console.error('Error signing out:', err);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#F8FAFF' }}>
      {/* Header */}
      <View style={styles.header}>
        <Image source={require('@/assets/images/safwa.png')} style={styles.logo} />
        <Text style={styles.headerTitle}>Profile</Text>
        <View style={styles.headerIcons}>
          <TouchableOpacity onPress={handleRefresh} disabled={loading || refreshing}>
            <Ionicons name="refresh-outline" size={24} color="#222" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView contentContainerStyle={{ padding: 16 }} showsVerticalScrollIndicator={false}>
        {loading || refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4AC29A" />
            <Text style={styles.loadingText}>Loading profile data...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Error: {error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {/* Profile Header */}
            <View style={styles.profileHeader}>
              <View style={styles.avatarContainer}>
                {assistant?.pictureUrl ? (
                  <Image source={{ uri: assistant.pictureUrl }} style={styles.avatar} />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Text style={styles.avatarText}>
                      {assistant?.firstName?.charAt(0) || ''}{assistant?.lastName?.charAt(0) || ''}
                    </Text>
                  </View>
                )}
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>
                  {assistant?.firstName} {assistant?.lastName}
                </Text>
                <Text style={styles.profileRole}>
                  {assistant?.cacheAdUser?.role?.name || 'Assistant'}
                </Text>
                <Text style={styles.profileEmail}>{assistant?.email}</Text>
              </View>
            </View>

            {/* Profile Details */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Personal Information</Text>
              <View style={styles.detailCard}>
                <View style={styles.detailRow}>
                  <MaterialIcons name="person" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Full Name:</Text>
                  <Text style={styles.detailValue}>
                    {assistant?.firstName} {assistant?.lastName}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="email" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Email:</Text>
                  <Text style={styles.detailValue}>{assistant?.email}</Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="phone" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Phone:</Text>
                  <Text style={styles.detailValue}>{assistant?.phone || 'Not provided'}</Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="badge" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>CIN:</Text>
                  <Text style={styles.detailValue}>{assistant?.cinNumber || 'Not provided'}</Text>
                </View>
                {assistant?.birthDate && (
                  <View style={styles.detailRow}>
                    <MaterialIcons name="cake" size={20} color="#4AC29A" />
                    <Text style={styles.detailLabel}>Birth Date:</Text>
                    <Text style={styles.detailValue}>
                      {new Date(assistant.birthDate).toLocaleDateString()}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Work Information */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Work Information</Text>
              <View style={styles.detailCard}>
                <View style={styles.detailRow}>
                  <MaterialIcons name="work" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Role:</Text>
                  <Text style={styles.detailValue}>
                    {assistant?.cacheAdUser?.role?.name || 'Not assigned'}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="code" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Code:</Text>
                  <Text style={styles.detailValue}>{assistant?.code || 'Not assigned'}</Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="location-on" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Zone:</Text>
                  <Text style={styles.detailValue}>{assistant?.zone?.name || 'Not assigned'}</Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialIcons name="people" size={20} color="#4AC29A" />
                  <Text style={styles.detailLabel}>Has Beneficiaries:</Text>
                  <Text style={styles.detailValue}>
                    {assistant?.hasBeneficiaries ? 'Yes' : 'No'}
                  </Text>
                </View>
                {assistant?.creationDate && (
                  <View style={styles.detailRow}>
                    <MaterialIcons name="date-range" size={20} color="#4AC29A" />
                    <Text style={styles.detailLabel}>Joined:</Text>
                    <Text style={styles.detailValue}>
                      {new Date(assistant.creationDate).toLocaleDateString()}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Sign Out Button */}
            <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
              <MaterialCommunityIcons name="logout" size={20} color="#FFFFFF" />
              <Text style={styles.signOutButtonText}>Sign Out</Text>
            </TouchableOpacity>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#F8FAFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  logo: {
    width: 43,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    color: '#222',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 300,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 300,
  },
  errorText: {
    fontSize: 16,
    color: '#FF5252',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#4AC29A',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4AC29A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 28,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 16,
    color: '#4AC29A',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#666',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 12,
  },
  detailCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#666',
    marginLeft: 10,
    width: 100,
  },
  detailValue: {
    fontSize: 15,
    color: '#222',
    flex: 1,
  },
  signOutButton: {
    backgroundColor: '#FF5252',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    marginTop: 20,
    marginBottom: Platform.OS === 'android' ? 30 : 100,
  },
  signOutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tokenInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 12,
    color: '#333',
    minHeight: 100,
    textAlignVertical: 'top',
    fontFamily: 'monospace',
  },
  refreshTokenButton: {
    backgroundColor: '#4AC29A',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 12,
  },
  refreshTokenButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 6,
  },
});
