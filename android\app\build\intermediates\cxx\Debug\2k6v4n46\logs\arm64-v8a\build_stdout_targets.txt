ninja: Entering directory `C:\Users\<USER>\assistant app\assisstant-app\android\app\.cxx\Debug\2k6v4n46\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[2/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[3/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[4/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[5/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[6/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[7/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[8/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[9/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[10/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[11/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[12/57] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[13/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[14/57] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[15/57] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[16/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6efa1af1a4f6d84b3d2b8ab5a1a32ba4/safeareacontext/RNCSafeAreaViewState.cpp.o
[17/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06cf8038f5abe83e62332628e08009af/renderer/components/safeareacontext/States.cpp.o
[18/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6efa1af1a4f6d84b3d2b8ab5a1a32ba4/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[19/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03a6119c8eabb5e714c4571f1617ac2e/components/safeareacontext/EventEmitters.cpp.o
[20/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8bbe6e78bc5f3ab4b3c95866f92d4e03/safeareacontext/safeareacontextJSI-generated.cpp.o
[21/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/76c83fd6d0176d433b4219fad47f8539/source/codegen/jni/safeareacontext-generated.cpp.o
[22/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03a6119c8eabb5e714c4571f1617ac2e/components/safeareacontext/ShadowNodes.cpp.o
[23/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06cf8038f5abe83e62332628e08009af/renderer/components/safeareacontext/Props.cpp.o
[24/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74fa7038b19ad68de4f24eed87b98874/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[25/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74fa7038b19ad68de4f24eed87b98874/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[26/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c8607cbd4b4756d0133f52fa70acc841/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[27/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8bbe6e78bc5f3ab4b3c95866f92d4e03/safeareacontext/ComponentDescriptors.cpp.o
[28/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/563c09355e037449cb7d215858db49d4/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[29/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5e6e3f83e1f230ab0946d30801df49f2/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[30/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74fa7038b19ad68de4f24eed87b98874/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[31/57] Linking CXX shared library "C:\Users\<USER>\assistant app\assisstant-app\android\app\build\intermediates\cxx\Debug\2k6v4n46\obj\arm64-v8a\libreact_codegen_safeareacontext.so"
[32/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/74fa7038b19ad68de4f24eed87b98874/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[33/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a373cb8be733972df68cc485ae14dced/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[34/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a373cb8be733972df68cc485ae14dced/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[35/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[36/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ef0f8e475649681230cdbf83a9720d4/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[37/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/76c35989152b910f0ddae2a8b1a2a4b9/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[38/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[39/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[40/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[41/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9013b94aab6d04cf1336812004c2f866/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[42/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[43/57] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/assistant_app/assisstant-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[44/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[45/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[46/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[47/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9013b94aab6d04cf1336812004c2f866/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[48/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[49/57] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[50/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[51/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9b5a3f93449c2db7b2ea5f6c5a987a63/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[52/57] Linking CXX shared library "C:\Users\<USER>\assistant app\assisstant-app\android\app\build\intermediates\cxx\Debug\2k6v4n46\obj\arm64-v8a\libreact_codegen_rnscreens.so"
[53/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[54/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[55/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[56/57] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[57/57] Linking CXX shared library "C:\Users\<USER>\assistant app\assisstant-app\android\app\build\intermediates\cxx\Debug\2k6v4n46\obj\arm64-v8a\libappmodules.so"
