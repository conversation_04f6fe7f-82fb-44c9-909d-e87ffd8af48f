{"name": "assisstant-app", "slug": "assisstant-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "assisstantapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"package": "com.abdoxm.assistantapp", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-notifications", {"icon": "./assets/images/safwa.png", "defaultChannel": "default", "enableBackgroundRemoteNotifications": false}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "534b9893-a79b-4a0a-877d-3e8f35d6c192"}}, "owner": "abdoxm", "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}