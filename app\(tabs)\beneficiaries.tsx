import { useDashboard } from '@/api/general';
import { getStoredAssistantData } from '@/api/general/assistantApi';
import { Beneficiary, getBeneficiariesByAssistantId } from '@/api/general/beneficiaryApi';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Image, Platform, RefreshControl, SafeAreaView, ScrollView, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

// Cache for storing beneficiaries data
let beneficiariesCache: Beneficiary[] | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export default function BeneficiariesScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([]);
  const { userActionsCount, loading } = useDashboard();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchBeneficiaries = async (forceRefresh = false) => {
    try {
      // Check if we can use cached data
      const now = Date.now();
      if (!forceRefresh && beneficiariesCache && (now - lastFetchTime) < CACHE_DURATION) {
        setBeneficiaries(beneficiariesCache);
        setIsLoading(false);
        setRefreshing(false);
        return;
      }

      setIsLoading(true);
      const assistantData = await getStoredAssistantData();

      if (!assistantData || !assistantData.id) {
        setError('Assistant data not found. Please log in again.');
        setIsLoading(false);
        return;
      }

      const beneficiariesData = await getBeneficiariesByAssistantId(assistantData.id);
      
      // Update cache
      beneficiariesCache = beneficiariesData;
      lastFetchTime = now;
      
      setBeneficiaries(beneficiariesData);
      setError(null);
    } catch (err) {
      console.error('Error fetching beneficiaries:', err);
      setError('Failed to load beneficiaries. Please try again later.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    void fetchBeneficiaries(true); // Force refresh
  }, []);

  // Fetch beneficiaries when component mounts
  useEffect(() => {
    fetchBeneficiaries();
  }, []);

  // Memoize filtered beneficiaries to prevent unnecessary recalculations
  const filteredBeneficiaries = useMemo(() => {
    return beneficiaries.filter((beneficiary: Beneficiary) => {
      const matchesSearch = beneficiary.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (beneficiary.independent && beneficiary.independent.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesStatus = filterStatus === 'All' || beneficiary.independent === filterStatus;

      return matchesSearch && matchesStatus;
    });
  }, [beneficiaries, searchQuery, filterStatus]);

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Header */}
      <View style={styles.header}>
        <Image source={require('@/assets/images/safwa.png')} style={styles.logo} />
        <Text style={styles.headerTitle}>Beneficiaires</Text>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={{marginTop: 4}} onPress={() => router.push('/actions')}>
            <Ionicons name="list-outline" size={24} color="#222" />
             {userActionsCount > 0 && (
              <View style={styles.badgeContainer}>
                <Text style={styles.badgeText}>{userActionsCount}</Text>
              </View>
            )} 
          </TouchableOpacity>
          <TouchableOpacity style={{ marginLeft: 12 }}>
            <Ionicons name="settings-outline" size={24} color="#222" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#888" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search beneficiaries..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#888" />
            </TouchableOpacity>
          )}
        </View>

        {/* Filter buttons */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {['All', 'Indépendant', 'Membre de famille'].map(status => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                filterStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setFilterStatus(status)}
            >
              <Text style={[
                styles.filterButtonText,
                filterStatus === status && styles.filterButtonTextActive
              ]}>
                {status}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Beneficiaries List */}
      <ScrollView 
        style={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4AC29A']}
            tintColor="#4AC29A"
          />
        }
      >
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4AC29A" />
            <Text style={styles.loadingText}>Loading beneficiaries...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchBeneficiaries}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : filteredBeneficiaries.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="people" size={48} color="#ccc" />
            <Text style={styles.emptyStateText}>No beneficiaries found</Text>
          </View>
        ) : (
          filteredBeneficiaries.map((beneficiary: Beneficiary) => (
            <TouchableOpacity
              key={beneficiary.id}
              style={styles.beneficiaryCard}
              onPress={() => {
                // Navigate to the beneficiary detail screen with essential data
                router.push({
                  pathname: '/beneficiary/[id]',
                  params: { 
                    id: beneficiary.id,
                    name: beneficiary.name,
                    independent: beneficiary.independent,
                    phoneNumber: beneficiary.phoneNumber || '',
                    email: beneficiary.email || '',
                    coordinates: beneficiary.coordinates?.latitude + '$' + beneficiary.coordinates?.longitude || '',
                  }
                });
                console.log("from benif",beneficiary.coordinates?.latitude + '$' + beneficiary.coordinates?.longitude)
              }}
            >
              <View style={styles.beneficiaryHeader}>
                <Text style={styles.beneficiaryName}>{beneficiary.name}</Text>
                <View style={[
                  styles.statusBadge,
                  beneficiary.independent && styles.statusActive,
                  !beneficiary.independent && styles.statusInactive,
                ]}>
                  <Text style={styles.statusText}>{beneficiary.independent}</Text>
                </View>
              </View>
              <View style={styles.beneficiaryDetails}>
                <Text style={styles.detailText}>
                  <Text style={styles.detailLabel}>Phone: </Text>
                  {beneficiary.phoneNumber || 'Not available'}
                </Text>
                <Text style={styles.detailText}>
                  <Text style={styles.detailLabel}>Email: </Text>
                  {beneficiary.email || 'Not available'}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8FAFF',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#F8FAFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  logo: {
    width: 43,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    color: '#222',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#F8FAFF',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  filterScroll: {
    marginTop: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#fff',
  },
  filterButtonActive: {
    backgroundColor: '#4AC29A',
  },
  filterButtonText: {
    color: '#555',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  listContainer: {
    flex: 1,
    padding: 16,
  },
  beneficiaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  beneficiaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  beneficiaryName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#e0e0e0',
  },
  statusActive: {
    backgroundColor: '#E6F9F2',
  },
  statusInactive: {
    backgroundColor: '#FFE8E8',
  },
  statusPending: {
    backgroundColor: '#FFF8E0',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#555',
  },
  beneficiaryDetails: {
    marginTop: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#444',
    marginBottom: 4,
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#888',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    marginTop: 12,
    fontSize: 16,
    color: '#888',
  },
  // Loading state styles
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#888',
  },
  // Error state styles
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  retryButton: {
    backgroundColor: '#4AC29A',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  badgeContainer: {
    position: 'absolute',
    top: -5,
    right: -8,
    backgroundColor: '#FF5252',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
});