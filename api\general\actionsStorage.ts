import AsyncStorage from '@react-native-async-storage/async-storage';
import { Action } from './dashboardApi';

// Keys for AsyncStorage
const ACTIONS_STORAGE_KEY = 'stored_actions';
const LAST_SEEN_ACTIONS_KEY = 'last_seen_actions_timestamp';
const UNREAD_ACTIONS_COUNT_KEY = 'unread_actions_count';
const ACTIONS_COUNT_KEY = 'actions_count';
/**
 * Stores the current actions in AsyncStorage
 * @param actions The actions to store
 */
export const storeActions = async (actions: Action[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(ACTIONS_STORAGE_KEY, JSON.stringify(actions));
    // Update the last seen timestamp
    await AsyncStorage.setItem(LAST_SEEN_ACTIONS_KEY, Date.now().toString());
    // Reset unread count to 0
    await AsyncStorage.setItem(UNREAD_ACTIONS_COUNT_KEY, '0');
    console.log('Actions stored successfully, unread count reset to 0');
  } catch (error) {
    console.error('Error storing actions:', error);
  }
};

/**
 * Retrieves the stored actions from AsyncStorage
 * @returns The stored actions or an empty array if none found
 */
export const getStoredActions = async (): Promise<Action[]> => {
  try {
    const data = await AsyncStorage.getItem(ACTIONS_STORAGE_KEY);
    if (data) {
      return JSON.parse(data) as Action[];
    }
    return [];
  } catch (error) {
    console.error('Error retrieving stored actions:', error);
    return [];
  }
};

/**
 * Gets the timestamp of when actions were last seen
 * @returns The timestamp or null if not found
 */
export const getLastSeenActionsTimestamp = async (): Promise<number | null> => {
  try {
    const timestamp = await AsyncStorage.getItem(LAST_SEEN_ACTIONS_KEY);
    return timestamp ? parseInt(timestamp) : null;
  } catch (error) {
    console.error('Error retrieving last seen actions timestamp:', error);
    return null;
  }
};

/**
 * Sets the unread actions count
 * @param count The count to set
 */
export const setUnreadActionsCount = async (count: number): Promise<void> => {
  try {
    await AsyncStorage.setItem(UNREAD_ACTIONS_COUNT_KEY, count.toString());
    console.log('Unread actions count set to:', count);
  } catch (error) {
    console.error('Error setting unread actions count:', error);
  }
};

export const setActionsCount = async (count: number): Promise<void> => {
  try {
    await AsyncStorage.setItem(ACTIONS_COUNT_KEY, count.toString());
    console.log('Unread actions count set to:', count);
  } catch (error) {
    console.error('Error setting unread actions count:', error);
  }
};

export const getActionsCount = async (): Promise<number> => {
  try {
    const count = await AsyncStorage.getItem(ACTIONS_COUNT_KEY);
    return count ? parseInt(count) : 0;
  } catch (error) {
    console.error('Error retrieving actions count:', error);
    return 0;
  }
};

/**
 * Gets the unread actions count
 * @returns The unread count or 0 if not found
 */
export const getUnreadActionsCount = async (): Promise<number> => {
  try {
    const count = await AsyncStorage.getItem(UNREAD_ACTIONS_COUNT_KEY);
    return count ? parseInt(count) : 0;
  } catch (error) {
    console.error('Error retrieving unread actions count:', error);
    return 0;
  }
};

/**
 * Compares two sets of actions to determine if there are new ones
 * @param newActions The new actions to compare
 * @param storedActions The stored actions to compare against
 * @returns The number of new actions
 */
export const compareActions = (newActions: Action[], storedActions: Action[]): number => {
  if (!storedActions || storedActions.length === 0) {
    return newActions.length;
  }

  // Create a map of stored action IDs for quick lookup
  const storedActionIds = new Set(
    storedActions.map(action => action.actionDTO?.id?.toString() || action.actionDto?.id?.toString())
  );

  // Count actions in newActions that aren't in storedActions
  let newCount = 0;
  for (const action of newActions) {
    const actionId = action.actionDTO?.id?.toString() || action.actionDto?.id?.toString();
    if (actionId && !storedActionIds.has(actionId)) {
      newCount++;
    }
  }

  return newCount;
};
