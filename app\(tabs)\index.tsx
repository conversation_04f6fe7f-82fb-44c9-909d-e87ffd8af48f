import { useAssistant, useDashboard } from '@/api/general';
import { getActionsCount, setActionsCount } from '@/api/general/actionsStorage';
import { dashboardStats, recentDonations } from '@/data/mockData';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useFocusEffect, useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Alert, Platform, RefreshControl, SafeAreaView, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../api/msalConfig/AuthContext';

export default function HomeScreen() {
  const router = useRouter();
  // Get dashboard data from API
  const { statistics, userActionsCount, userActions, loading, refreshDashboardData } = useDashboard();

  // Get assistant data from API
  const { assistant, loading: assistantLoading, error } = useAssistant();

  const { user, signOut, clearStorage } = useAuth();
  const [isClearing, setIsClearing] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [userActionsCountState, setUserActionsCountState] = useState(0);

  // Check for new actions when the screen comes into focus
  useEffect(() => {
    // Check token every 5 minutes
    const tokenCheckInterval = setInterval(async () => {
      console.log('Performing scheduled token check...');
      await refreshDashboardData();
          return () => {
            // Cleanup function when screen loses focus (if needed)
          };
    }, 5 * 1000); // 5 minutes in milliseconds

    return () => {
      clearInterval(tokenCheckInterval);
    };
  }, []);

  // Handle clear storage
  const handleClearStorage = async () => {
    try {
      setIsClearing(true);
      await clearStorage();
      Alert.alert('Success', 'Storage cleared successfully');
    } catch (err) {
      console.error('Error clearing storage:', err);
      Alert.alert('Error', 'Failed to clear storage');
    } finally {
      setIsClearing(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    try {
      setRefreshing(true);
      await refreshDashboardData();
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    console.log('client id:', process.env.EXPO_PUBLIC_AZURE_CLIENT_ID);
  }, [user]);

  // Debug actions count
  useFocusEffect(
    useCallback(() => {
      getActionsCount().then((count: number) => {
        console.log('Current user actions count:', count);
        setUserActionsCountState(count);
        if(userActions.length > 0){
          setActionsCount(userActions.length);
        }
      });

  }, [userActions.length > 0]));

  // Create dashboard stats from API data
  const updatedDashboardStats = useMemo(() => {
    if (!statistics) {
      return dashboardStats; // Use mock data if API data is not available
    }

    return [
      {
        title: 'Donations',
        value: `${statistics.totalDonationAmount.toLocaleString()} Dhs`,
        icon: 'attach-money' as const,
        color: '#4AC29A',
        bgColor: '#E6F9F2',
      },
      {
        title: 'Actions',
        value: userActions.length > 0 ? userActions.length : userActionsCountState.toString(),
        icon: 'event' as const,
        color: '#C2994A',
        bgColor: '#F9F6E6',
      },
      {
        title: 'Bénéficiaires',
        value: statistics.totalBeneficiaries.toString(),
        icon: 'people' as const,
        color: '#4A7FC2',
        bgColor: '#E6EFF9',
      },
      {
        title: 'Donateurs',
        value: statistics.totalDonors.toString(),
        icon: 'volunteer-activism' as const,
        color: '#C24A9D',
        bgColor: '#F9E6F6',
      },
    ];
  }, [statistics, userActionsCountState]);

  // Get the assistant's full name
  const assistantName = assistant ? `${assistant.firstName} ${assistant.lastName}` : user?.name || 'User';

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Header */}
      <View style={styles.header}>
        <Image  source={require('@/assets/images/safwa.png')} style={styles.logo} />
        <Text style={styles.headerTitle}>Home</Text>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={{marginTop: 4}} onPress={() => router.push('/actions')}>
            <Ionicons name="list-outline" size={24} color="#222" />
            {userActionsCount > 0 && (
              <View style={styles.badgeContainer}>
                <Text style={styles.badgeText}>{userActionsCount}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity style={{ marginLeft: 12 }}>
            <Ionicons name="settings-outline" size={24} color="#222" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4AC29A']}
          />
        }
      >
        {/* Greeting */}
        <View style={styles.greetingContainer}>
          <Text style={styles.wave}>👋</Text>
          <View>
            <Text style={styles.greetingText}>Bonjour!</Text>
            {assistantLoading ? (
              <ActivityIndicator size="small" color="#4AC29A" />
            ) : (
              <Text style={styles.userName}>{assistantName}</Text>
            )}
          </View>
        </View>

        {/* Dashboard Stats - Horizontal Scrollable */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4AC29A" />
          </View>
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.statsContainer}
          >
            {updatedDashboardStats.map((stat, index) => (
              <TouchableOpacity key={index} style={styles.statCard} disabled={stat.title !== 'Actions'} onPress={() => stat.title === 'Actions' && router.push('/actions')}>
                <View style={[styles.statIconContainer, { backgroundColor: stat.bgColor }]}>
                  <MaterialIcons name={stat.icon} size={24} color={stat.color} />
                </View>
                <Text style={styles.statTitle}>{stat.title}</Text>
                <Text style={styles.statValue}>{stat.value}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}

        {/* Recent Donations */}
        <View style={{ marginTop: 24, marginBottom: Platform.OS === 'android' ? 20 : 50 }}>
          <View style={styles.recentHeader}>
            <Text style={styles.recentTitle}>Dons récents</Text>
            <TouchableOpacity>
              <Text style={styles.seeAll}>Voir tout {'>'}</Text>
            </TouchableOpacity>
          </View>
          {recentDonations.map((don, idx) => (
            <View key={idx} style={styles.donationCard}>
              <View style={styles.donationRow}>
                <Text style={styles.donationDate}>{don.date}</Text>
                <Text style={styles.donationAmount}>{don.amount}</Text>
                <View style={styles.statusBadge}>
                  <Text style={styles.statusText}>{don.status}</Text>
                </View>
              </View>
              <View style={{ marginTop: 8 }}>
                <Text style={styles.donationDetail}><Text style={styles.detailLabel}>Service:</Text> {don.service}</Text>
                <Text style={styles.donationDetail}><Text style={styles.detailLabel}>Type:</Text> {don.type}</Text>
                <Text style={styles.donationDetail}><Text style={styles.detailLabel}>Channel:</Text> {don.channel}</Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8FAFF',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#F8FAFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  logo: {
    width: 43,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    color: '#222',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greetingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 12,
  },
  wave: {
    fontSize: 28,
    marginRight: 8,
  },
  greetingText: {
    fontSize: 16,
    color: '#888',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#222',
  },
  // Dashboard Stats Styles
  statsContainer: {
    paddingVertical: 5,
    paddingHorizontal: 4,
    gap: 10,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 15,
    width: 130,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 2,
    elevation: 1,
    alignItems: 'center',
  },
  statIconContainer: {
    borderRadius: 12,
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  statTitle: {
    fontSize: 15,
    color: '#888',
    marginBottom: 6,
    textAlign: 'center',
  },
  statValue: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#222',
    textAlign: 'center',
  },
  recentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  seeAll: {
    color: '#4AC29A',
    fontWeight: '600',
  },
  donationCard: {
    backgroundColor: '#fff',
    borderRadius: 14,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.03,
    shadowRadius: 6,
    elevation: 1,
  },
  donationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  donationDate: {
    color: '#888',
    fontSize: 14,
    flex: 1,
  },
  donationAmount: {
    color: '#19B37A',
    fontWeight: 'bold',
    fontSize: 18,
    marginHorizontal: 8,
  },
  statusBadge: {
    backgroundColor: '#E6F9F2',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 2,
  },
  statusText: {
    color: '#19B37A',
    fontWeight: 'bold',
    fontSize: 13,
  },
  donationDetail: {
    color: '#444',
    fontSize: 14,
    marginTop: 2,
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#888',
  },
  clearStorageContainer: {
    marginTop: 30,
    marginBottom: Platform.OS === 'android' ? 20 : 50,
    alignItems: 'center',
  },
  clearStorageButton: {
    backgroundColor: '#FF5252', // Red color for warning
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
  },
  clearStorageButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  badgeContainer: {
    position: 'absolute',
    top: -5,
    right: -8,
    backgroundColor: '#FF5252',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
});


