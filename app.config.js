// Load environment variables
const path = require('path');
const dotenv = require('dotenv');

// Load the .env file
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Get environment variables
const {
  EXPO_PUBLIC_AZURE_CLIENT_ID,
  EXPO_PUBLIC_AZURE_TENANT_ID,
  EXPO_PUBLIC_AZURE_REDIRECT_URI,
  EXPO_PUBLIC_AZURE_AUTHORITY,
  EXPO_PUBLIC_AZURE_SCOPES
} = process.env;

module.exports = {
  name: "assisstant-app",
  slug: "assisstant-app",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/icon.png",
  scheme: "assisstantapp",
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.yourcompany.yourauthapp"
  },
  android: {
    package: "com.abdoxm.assistantapp",
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff"
    },
    edgeToEdgeEnabled: true
  },
  web: {
    bundler: "metro",
    output: "static",
    favicon: "./assets/images/favicon.png"
  },
  plugins: [
    "expo-router",
    [
      "expo-splash-screen",
      {
        "image": "./assets/images/splash-icon.png",
        "imageWidth": 200,
        "resizeMode": "contain",
        "backgroundColor": "#ffffff"
      }
    ],
    [
      "expo-notifications",
      {
        "icon": "./assets/images/safwa.png",
        "defaultChannel": "default",
        "enableBackgroundRemoteNotifications": false
      }
    ]
  ],
  experiments: {
    typedRoutes: true
  },
  extra: {
    router: {},
    eas: {
      projectId: "534b9893-a79b-4a0a-877d-3e8f35d6c192"
    },
    // Add environment variables here
    EXPO_PUBLIC_AZURE_CLIENT_ID: EXPO_PUBLIC_AZURE_CLIENT_ID,
    EXPO_PUBLIC_AZURE_TENANT_ID: EXPO_PUBLIC_AZURE_TENANT_ID,
    EXPO_PUBLIC_AZURE_REDIRECT_URI: EXPO_PUBLIC_AZURE_REDIRECT_URI || "assisstantapp://auth",
    EXPO_PUBLIC_AZURE_AUTHORITY: EXPO_PUBLIC_AZURE_AUTHORITY,
    EXPO_PUBLIC_AZURE_SCOPES: EXPO_PUBLIC_AZURE_SCOPES,
  },
  scheme: "assisstantapp",
  owner: "abdoxm"
};
