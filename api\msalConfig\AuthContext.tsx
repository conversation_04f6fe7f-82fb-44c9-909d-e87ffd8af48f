import { PublicClientApplication } from '@azure/msal-browser';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as AuthSession from 'expo-auth-session';
import * as Notifications from 'expo-notifications';
import * as WebBrowser from 'expo-web-browser';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';

import { ApiClient } from '../general/apiClient';
import { getAssistantByEmail } from '../general/assistantApi';
import { loginRequest, msalConfigMobile, msalConfigWeb } from './msalConfig';

// Register the redirect URI for web authentication
WebBrowser.maybeCompleteAuthSession();

// Define the authentication context type
interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  error: string | null;
  signIn: () => Promise<void>;
  signInWithEmailPassword: (email: string, password: string) => Promise<boolean>; // Returns success status
  signOut: () => Promise<void>;
  clearStorage: () => Promise<void>; // Added function to clear storage for debugging
  refreshToken: () => Promise<boolean>; // Added refreshToken function
  checkAndRefreshTokenIfNeeded: () => Promise<boolean>; // Added function to check and refresh token if needed
  getAccessToken: () => Promise<string | null>; // Get a valid access token for API requests
  registerDeviceToken: () => Promise<void>; // Added function to register device token
}

// Create the authentication context
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  user: null,
  error: null,
  signIn: async () => {},
  signInWithEmailPassword: async () => false, // Return false by default
  signOut: async () => {},
  clearStorage: async () => {},
  refreshToken: async () => false,
  checkAndRefreshTokenIfNeeded: async () => false,
  getAccessToken: async () => null,
  registerDeviceToken: async () => {},
});

// Initialize MSAL for web
let msalInstance: PublicClientApplication | null = null;
if (Platform.OS === 'web') {
  msalInstance = new PublicClientApplication(msalConfigWeb);
}

// Provider component for the authentication context
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Register device token function
  const registerDeviceToken = async () => {
    try {
      if (Platform.OS === 'web') {
        return; // Skip for web platform
      }

      // Request notification permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Get the token
      const token = await Notifications.getExpoPushTokenAsync();
      console.log('Token:', token);
      
      // Get the assistant data using the user's email
      if (user && user.email) {
        const assistant = await getAssistantByEmail(user.email);
        if (assistant && assistant.id) {
          // Send the token to the server
          await ApiClient.put(`/assistants/${assistant.id}/device-token`, {
            device_token: token.data
          });
          
          // Store the token locally
          await AsyncStorage.setItem('device_token', token.data);
          console.log('Device token registered successfully');
        }
      }
    } catch (error) {
      console.error('Error registering device token:', error);
    }
  };

  // Set up periodic token refresh and device token registration
  useEffect(() => {
    // Only set up the interval if the user is authenticated
    if (isAuthenticated && user) {
      console.log('Setting up token refresh interval check');

      // Register device token when user is authenticated
      registerDeviceToken();

      // Check token every 5 minutes
      const tokenCheckInterval = setInterval(async () => {
        console.log('Performing scheduled token check...');
        await checkAndRefreshTokenIfNeeded();
      }, 5 * 60 * 1000); // 5 minutes in milliseconds

      // Clean up the interval when the component unmounts or auth state changes
      return () => {
        console.log('Clearing token refresh interval');
        clearInterval(tokenCheckInterval);
      };
    }
  }, [isAuthenticated, user]);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        if (Platform.OS === 'web') {
          // Web platform initialization
          if (msalInstance) {
            const accounts = msalInstance.getAllAccounts();
            if (accounts.length > 0) {
              try {
                msalInstance.setActiveAccount(accounts[0]);
                // Try to silently acquire a token to verify the session is still valid
                const result = await msalInstance.acquireTokenSilent({
                  ...loginRequest,
                  account: accounts[0],
                });

                // If we get here, the token is valid
                setUser(result.account);
                setIsAuthenticated(true);
              } catch (error) {
                console.error('Token validation failed during initialization:', error);
                // Token is invalid or expired, clear the session
                setUser(null);
                setIsAuthenticated(false);
              }
            }
          }
        } else {
          // Mobile platform initialization
          const token = await AsyncStorage.getItem('auth_token');
          const userData = await AsyncStorage.getItem('user_data');

          if (token && userData) {
            const parsedUserData = JSON.parse(userData);

            // Check if token is expired
            if (parsedUserData.expiresOn && new Date(parsedUserData.expiresOn) > new Date()) {
              // Calculate and log remaining time until token expiry
              const expiryDate = new Date(parsedUserData.expiresOn);
              const currentDate = new Date();
              const remainingTimeMs = expiryDate.getTime() - currentDate.getTime();
              const remainingTimeSeconds = Math.floor(remainingTimeMs / 1000);

              console.log(`Token valid for ${remainingTimeSeconds} more seconds (expires at ${expiryDate.toLocaleString()})`);

              // Since the token is not expired, we'll consider it valid
              // and only verify it if we need to make API calls
              setUser(parsedUserData);
              setIsAuthenticated(true);

              // We'll do a background check to see if the token works with Graph API
              // but we won't block the user experience on this
              fetch("https://graph.microsoft.com/v1.0/me", {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }).then(response => {
                if (!response.ok) {
                  console.log('Token appears valid but Graph API request failed. Will use token until expiration.');
                }
              }).catch(error => {
                console.log('Error checking token with Graph API:', error);
                // We'll still keep the user logged in since the token isn't expired
              });
            } else {
              // Token is expired, check if we can refresh it
              if (parsedUserData.refreshToken) {
                if (parsedUserData.expiresOn) {
                  const expiryDate = new Date(parsedUserData.expiresOn);
                  const currentDate = new Date();
                  const timeSinceExpiryMs = currentDate.getTime() - expiryDate.getTime();
                  const timeSinceExpirySeconds = Math.floor(timeSinceExpiryMs / 1000);

                  console.log(`Access token expired ${timeSinceExpirySeconds} seconds ago (at ${expiryDate.toLocaleString()}), attempting to refresh...`);
                } else {
                  console.log('Access token expired (no expiry date found), attempting to refresh...');
                }

                try {
                  // Configure the discovery document for token endpoint
                  const discovery = {
                    tokenEndpoint: `${msalConfigMobile.auth.authority}/oauth2/v2.0/token`,
                  };

                  // Create the refresh token request
                  const refreshTokenRequestBody = new URLSearchParams({
                    client_id: msalConfigMobile.auth.clientId,
                    scope: msalConfigMobile.scopes.join(' '),
                    refresh_token: parsedUserData.refreshToken,
                    grant_type: 'refresh_token',
                  }).toString();

                  // Make the refresh token request
                  const tokenResponse = await fetch(discovery.tokenEndpoint, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: refreshTokenRequestBody,
                  });

                  const tokenData = await tokenResponse.json();

                  if (tokenResponse.ok && tokenData.access_token) {
                    console.log('Token refresh successful');

                    // Create a new user object with the refreshed tokens
                    const expiryDate = new Date(Date.now() + (tokenData.expires_in || 3600) * 1000);
                    const refreshedUser = {
                      ...parsedUserData,
                      accessToken: tokenData.access_token,
                      idToken: tokenData.id_token || parsedUserData.idToken,
                      refreshToken: tokenData.refresh_token || parsedUserData.refreshToken,
                      expiresOn: expiryDate,
                    };

                    console.log(`New access token obtained. Expires in ${tokenData.expires_in || 3600} seconds (at ${expiryDate.toLocaleString()})`);

                    // Save the new tokens
                    await AsyncStorage.setItem('auth_token', tokenData.access_token);
                    await AsyncStorage.setItem('user_data', JSON.stringify(refreshedUser));

                    // Update the authentication state
                    setUser(refreshedUser);
                    setIsAuthenticated(true);
                    return; // Exit early since we've successfully refreshed the token
                  } else {
                    console.error('Failed to refresh token:', tokenData.error_description || 'Unknown error');
                  }
                } catch (error) {
                  console.error('Error refreshing token:', error);
                }
              }

              // If we get here, either there's no refresh token or the refresh failed
              if (parsedUserData.expiresOn) {
                const expiryDate = new Date(parsedUserData.expiresOn);
                console.log(`Could not refresh token. Access token expired at ${expiryDate.toLocaleString()}, clearing session...`);
              } else {
                console.log('Could not refresh token. Access token expired (no expiry date found), clearing session...');
              }

              // Clear the session
              await AsyncStorage.removeItem('auth_token');
              await AsyncStorage.removeItem('user_data');
              await AsyncStorage.removeItem('access_token');
              setUser(null);
              setIsAuthenticated(false);
            }
          } else {
            // No token or user data, user needs to log in
            setUser(null);
            setIsAuthenticated(false);
          }
        }
      } catch (err) {
        console.error('Authentication initialization error:', err);
        setError('Failed to initialize authentication');
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Sign in function
  const signIn = async () => {
    try {
      if (Platform.OS === 'web') {
        // Web platform sign in
        if (msalInstance) {
          const result = await msalInstance.loginPopup(loginRequest);
          setUser(result.account);
          setIsAuthenticated(true);
          await registerDeviceToken(); // Register device token after successful sign in
        }
      } else {
        // Mobile platform sign in
        const redirectUri = AuthSession.makeRedirectUri({
          scheme: 'assisstantapp',
          path: 'redirect',
        });

        const authRequest = new AuthSession.AuthRequest({
          clientId: msalConfigMobile.auth.clientId,
          scopes: msalConfigMobile.scopes,
          redirectUri,
          prompt: AuthSession.Prompt.Login,
        });

        const result = await authRequest.promptAsync(msalConfigMobile.auth.authority);

        if (result.type === 'success' && result.authentication) {
          const { accessToken, idToken, refreshToken } = result.authentication;
          const expiresIn = result.authentication.expiresIn || 3600; // Default to 1 hour if not provided

          // Calculate token expiration
          const expiresOn = new Date(Date.now() + expiresIn * 1000);

          // Create user object
          const userData = {
            accessToken,
            idToken,
            refreshToken,
            expiresOn,
            email: '', // We'll get this from the token claims
            name: '', // We'll get this from the token claims
          };

          // Save tokens and user data
          await AsyncStorage.setItem('auth_token', accessToken);
          await AsyncStorage.setItem('user_data', JSON.stringify(userData));

          // Update state
          setUser(userData);
          setIsAuthenticated(true);
          await registerDeviceToken(); // Register device token after successful sign in
        }
      }
    } catch (err) {
      console.error('Sign in error:', err);
      setError('Failed to sign in');
      throw err;
    }
  };

  // Refresh token function
  const refreshToken = async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Only applicable for mobile platforms
      if (Platform.OS !== 'web') {
        const userData = await AsyncStorage.getItem('user_data');

        if (userData) {
          const parsedUserData = JSON.parse(userData);

          if (parsedUserData.refreshToken) {
            console.log('Manually refreshing token...');

            // Configure the discovery document for token endpoint
            const discovery = {
              tokenEndpoint: `${msalConfigMobile.auth.authority}/oauth2/v2.0/token`,
            };

            // Create the refresh token request
            const refreshTokenRequestBody = new URLSearchParams({
              client_id: msalConfigMobile.auth.clientId,
              scope: msalConfigMobile.scopes.join(' '),
              refresh_token: parsedUserData.refreshToken,
              grant_type: 'refresh_token',
            }).toString();

            // Make the refresh token request
            const tokenResponse = await fetch(discovery.tokenEndpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: refreshTokenRequestBody,
            });

            const tokenData = await tokenResponse.json();

            if (tokenResponse.ok && tokenData.access_token) {
              console.log('Manual token refresh successful');

              // Create a new user object with the refreshed tokens
              const expiryDate = new Date(Date.now() + (tokenData.expires_in || 3600) * 1000);
              const refreshedUser = {
                ...parsedUserData,
                accessToken: tokenData.access_token,
                idToken: tokenData.id_token || parsedUserData.idToken,
                refreshToken: tokenData.refresh_token || parsedUserData.refreshToken,
                expiresOn: expiryDate,
              };

              console.log(`New access token obtained. Expires in ${tokenData.expires_in || 3600} seconds (at ${expiryDate.toLocaleString()})`);

              // Save the new tokens
              await AsyncStorage.setItem('auth_token', tokenData.access_token);
              await AsyncStorage.setItem('user_data', JSON.stringify(refreshedUser));

              // Update the authentication state
              setUser(refreshedUser);
              setIsAuthenticated(true);
              return true;
            } else {
              console.error('Failed to refresh token:', tokenData.error_description || 'Unknown error');
            }
          } else {
            console.error('No refresh token available');
          }
        } else {
          console.error('No user data available');
        }
      } else {
        // For web, use MSAL's built-in token refresh
        if (msalInstance) {
          const accounts = msalInstance.getAllAccounts();
          if (accounts.length > 0) {
            try {
              msalInstance.setActiveAccount(accounts[0]);
              const result = await msalInstance.acquireTokenSilent({
                ...loginRequest,
                account: accounts[0],
              });

              setUser(result.account);
              setIsAuthenticated(true);
              return true;
            } catch (error) {
              console.error('Failed to refresh token on web:', error);
            }
          }
        }
      }

      return false;
    } catch (err) {
      console.error('Token refresh error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setIsLoading(true);

      if (Platform.OS === 'web') {
        // Web sign-out
        if (msalInstance) {
          await msalInstance.logoutPopup();
        }
      } else {
        // Mobile sign-out
        await AsyncStorage.removeItem('auth_token');
        await AsyncStorage.removeItem('user_data');
      }

      setUser(null);
      setIsAuthenticated(false);
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message || 'Failed to sign out');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to check if token is about to expire and refresh it if needed
  const checkAndRefreshTokenIfNeeded = async (): Promise<boolean> => {
    try {
      // Only applicable for mobile platforms
      if (Platform.OS !== 'web') {
        const userData = await AsyncStorage.getItem('user_data');

        if (userData) {
          const parsedUserData = JSON.parse(userData);

          if (parsedUserData.expiresOn) {
            const expiryDate = new Date(parsedUserData.expiresOn);
            const currentDate = new Date();
            const timeUntilExpiryMs = expiryDate.getTime() - currentDate.getTime();
            const timeUntilExpiryMinutes = timeUntilExpiryMs / (1000 * 60);

            // If token will expire in less than 5 minutes, refresh it
            if (timeUntilExpiryMinutes < 5) {
              console.log(`Token will expire in ${timeUntilExpiryMinutes.toFixed(2)} minutes, refreshing proactively...`);
              return await refreshToken();
            } else {
              console.log(`Token is still valid for ${timeUntilExpiryMinutes.toFixed(2)} minutes, no need to refresh yet.`);
              return true; // Token is still valid
            }
          } else if (parsedUserData.refreshToken) {
            // If we have a refresh token but no expiry date, refresh to be safe
            console.log('No expiry date found but refresh token available, refreshing to be safe...');
            return await refreshToken();
          }
        }
      } else if (msalInstance) {
        // For web, MSAL handles token refresh automatically
        return true;
      }

      return false;
    } catch (err) {
      console.error('Error checking token expiry:', err);
      return false;
    }
  };

  // Function to get a valid access token for API requests
  const getAccessToken = async (): Promise<string | null> => {
    try {
      // First check if token needs refresh
      const isTokenValid = await checkAndRefreshTokenIfNeeded();

      if (!isTokenValid) {
        console.log('Could not get a valid token');
        return null;
      }

      if (Platform.OS === 'web') {
        if (msalInstance) {
          const accounts = msalInstance.getAllAccounts();
          if (accounts.length > 0) {
            try {
              msalInstance.setActiveAccount(accounts[0]);
              const result = await msalInstance.acquireTokenSilent({
                ...loginRequest,
                account: accounts[0],
              });

              return result.accessToken;
            } catch (error) {
              console.error('Failed to get access token on web:', error);
            }
          }
        }
      } else {
        // For mobile, get the token from storage
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          return token;
        }
      }

      return null;
    } catch (err) {
      console.error('Error getting access token:', err);
      return null;
    }
  };

  // Sign in with email and password
  const signInWithEmailPassword = async (email: string, password: string): Promise<boolean> => {
    try {
      // Configure the discovery document for token endpoint
      const discovery = {
        tokenEndpoint: `${msalConfigMobile.auth.authority}/oauth2/v2.0/token`,
      };

      // Create the token request body
      const tokenRequestBody = new URLSearchParams({
        client_id: msalConfigMobile.auth.clientId,
        scope: msalConfigMobile.scopes.join(' '),
        username: email,
        password: password,
        grant_type: 'password',
      }).toString();

      // Make the token request
      const tokenResponse = await fetch(discovery.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenRequestBody,
      });

      const tokenData = await tokenResponse.json();

      if (tokenResponse.ok && tokenData.access_token) {
        // Calculate token expiration
        const expiresOn = new Date(Date.now() + (tokenData.expires_in || 3600) * 1000);

        // Create user object
        const userData = {
          accessToken: tokenData.access_token,
          idToken: tokenData.id_token,
          refreshToken: tokenData.refresh_token,
          expiresOn,
          email: email,
          name: email.split('@')[0], // Use email username as name
        };

        // Save tokens and user data
        await AsyncStorage.setItem('auth_token', tokenData.access_token);
        await AsyncStorage.setItem('user_data', JSON.stringify(userData));

        // Update state
        setUser(userData);
        setIsAuthenticated(true);
        await registerDeviceToken(); // Register device token after successful sign in
        return true;
      } else {
        console.error('Failed to sign in:', tokenData.error_description || 'Unknown error');
        return false;
      }
    } catch (error) {
      console.error('Error signing in with email and password:', error);
      return false;
    }
  };

  // Function to clear storage for debugging purposes
  const clearStorage = async () => {
    try {
      setIsLoading(true);

      // Clear all authentication-related storage
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
      await AsyncStorage.removeItem('access_token');

      // For web, clear MSAL cache if available
      if (Platform.OS === 'web' && msalInstance) {
        const accounts = msalInstance.getAllAccounts();
        for (const account of accounts) {
          msalInstance.logoutRedirect({ account });
        }
      }

      console.log('Storage cleared successfully');

      // Reset authentication state
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
    } catch (err: any) {
      console.error('Error clearing storage:', err);
      setError(err.message || 'Failed to clear storage');
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const value = {
    isAuthenticated,
    isLoading,
    user,
    error,
    signIn,
    signInWithEmailPassword,
    signOut,
    clearStorage,
    refreshToken,
    checkAndRefreshTokenIfNeeded,
    getAccessToken,
    registerDeviceToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use the authentication context
export const useAuth = () => useContext(AuthContext);
