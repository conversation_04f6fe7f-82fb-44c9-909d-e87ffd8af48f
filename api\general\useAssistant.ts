import { useEffect, useState } from 'react';
import { Assistant, getCurrentUserAssistantData } from './assistantApi';

/**
 * Hook to get and manage assistant data
 * @returns An object containing the assistant data and loading state
 */
export const useAssistant = () => {
  const [assistant, setAssistant] = useState<Assistant | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssistantData = async () => {
      try {
        setLoading(true);
        const data = await getCurrentUserAssistantData();
        setAssistant(data);
        setError(null);
      } catch (err) {
        console.error('Error in useAssistant hook:', err);
        setError('Failed to load assistant data');
      } finally {
        setLoading(false);
      }
    };

    fetchAssistantData();
  }, []);

  /**
   * Refreshes the assistant data
   */
  const refreshAssistantData = async () => {
    try {
      setLoading(true);
      const data = await getCurrentUserAssistantData();
      setAssistant(data);
      setError(null);
      return data;
    } catch (err) {
      console.error('Error refreshing assistant data:', err);
      setError('Failed to refresh assistant data');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    assistant,
    loading,
    error,
    refreshAssistantData,
  };
};
