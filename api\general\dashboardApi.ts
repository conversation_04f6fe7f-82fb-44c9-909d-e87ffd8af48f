import { ApiClient } from './apiClient';

/**
 * Interface for the general statistics response
 */
export interface GeneralStatistics {
  totalDonors: number;
  totalBeneficiaries: number;
  totalDonationAmount: number;
  totalKafalat: number;
}

/**
 * Interface for user profile
 */
export interface UserProfile {
  id: number;
  nameProfile: string;
  moduleFunctionalities: Record<string, string[]>;
}

/**
 * Interface for user role
 */
export interface UserRole {
  id: number;
  name: string;
  code: string;
  description: string;
  rolePrivileges: any;
}

/**
 * Interface for user
 */
export interface User {
  id: number;
  assistantId: number | null;
  zoneId: number | null;
  azureDirectoryId: string;
  firstName: string;
  lastName: string;
  mail: string;
  creationDate: string;
  updateDate: string;
  profile: UserProfile;
  role: UserRole;
  lastLoginInDate: string;
}

/**
 * Interface for comment
 */
export interface Comment {
  id: number;
  date: string;
  content: string;
  labelComment: boolean;
  author: User;
}

/**
 * Interface for action status
 */
export interface ActionStatus {
  id: number;
  code: string;
  name: string;
  nameAr: string | null;
  nameEn: string | null;
}

/**
 * Interface for the action DTO
 */
export interface ActionDto {
  id: number;
  dateEntry: string;
  deadline: string;
  dateRealize: string | null;
  createdBy: User;
  comments: Comment[];
  subject: string;
  affectedTo: User;
  actionStatus: ActionStatus;
}

/**
 * Interface for the action
 */
export interface Action {
  actionDTO: ActionDto;
  entityId: number;
  entityType: string;
  moduleName: string;
  modulepath: string;
  // Allow dynamic access to properties
  [key: string]: any;
}

export interface ActionsResponse {
  content: Action[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

/**
 * Fetches general statistics from the API
 * @returns The general statistics data
 */
export const getGeneralStatistics = async (): Promise<GeneralStatistics> => {
  try {
    // Use the ApiClient to make the request
    const data = await ApiClient.get<GeneralStatistics>('/dashboard/general-statistics');
    return data;
  } catch (error) {
    console.error('Error fetching general statistics:', error);
    // Return default values in case of error
    return {
      totalDonors: 0,
      totalBeneficiaries: 0,
      totalDonationAmount: 0,
      totalKafalat: 0
    };
  }
};

/**
 * Fetches actions from the API
 * @param page The page number to fetch (0-based)
 * @returns The actions data
 */
export const getActions = async (page: number = 0): Promise<ActionsResponse> => {
  try {
    // Use the ApiClient to make the request
    const responseData = await ApiClient.get<any>(`/actions/all`, { page: page.toString() });

    // Check if the response is an array (as in the example provided)
    if (Array.isArray(responseData)) {
      return {
        content: responseData,
        totalElements: responseData.length,
        totalPages: 1,
        size: responseData.length,
        number: 0
      };
    }

    // If it's already in the expected format
    if (responseData.content) {
      console.log('API returned paginated actions');
      return responseData as ActionsResponse;
    }

    // If it's in some other format, try to adapt it
    console.log('API returned unexpected format, adapting...');
    return {
      content: Array.isArray(responseData) ? responseData : [responseData],
      totalElements: Array.isArray(responseData) ? responseData.length : 1,
      totalPages: 1,
      size: Array.isArray(responseData) ? responseData.length : 1,
      number: 0
    };
  } catch (error) {
    console.error('Error fetching actions:', error);
    // Return empty data in case of error
    return {
      content: [],
      totalElements: 0,
      totalPages: 0,
      size: 0,
      number: 0
    };
  }
};

/**
 * Filters actions by user email
 * @param actions The list of actions to filter
 * @param email The email to filter by
 * @returns The filtered actions
 */
export const filterActionsByEmail = (actions: Action[], email: string): Action[] => {
  if (!actions || !email) {
    console.warn('No actions or email provided for filtering');
    return [];
  }


  // Normalize email for case-insensitive comparison
  const normalizedEmail = email.toLowerCase().trim();

  // Log the first few actions to debug

  // Filter actions based on the correct structure from the API
  const filteredActions = actions.filter(action => {
    // Check if the action has the correct structure
    if (action.actionDTO && action.actionDTO.affectedTo && action.actionDTO.affectedTo.mail) {
      const actionEmail = action.actionDTO.affectedTo.mail;
      const normalizedActionEmail = actionEmail.toLowerCase().trim();
      return normalizedActionEmail === normalizedEmail;
    }

    // Fallback for backward compatibility
    if (action.actionDto && action.actionDto.affectedTo && action.actionDto.affectedTo.mail) {
      const actionEmail = action.actionDto.affectedTo.mail;
      const normalizedActionEmail = actionEmail.toLowerCase().trim();
      return normalizedActionEmail === normalizedEmail;
    }

    return false;
  });
  return filteredActions;
};
