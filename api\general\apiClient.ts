import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the base URL for the API
export const API_BASE_URL = 'http://172.22.4.28:8001';

/**
 * Generic API client for making HTTP requests
 */
export class ApiClient {
  /**
   * Makes a GET request to the API
   * @param endpoint The API endpoint to call
   * @param params Optional query parameters
   * @returns The response data
   */
  static async get<T>(endpoint: string, params: Record<string, string> = {}): Promise<T> {
    try {
      // Get the access token
      const token = await AsyncStorage.getItem('auth_token');

      if (!token) {
        throw new Error('No access token available');
      }

      // Build the URL with query parameters
      const url = new URL(`${API_BASE_URL}${endpoint}`);
      Object.keys(params).forEach(key => {
        url.searchParams.append(key, params[key]);
      });

      // Make the request
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${errorText}`);
      }

      // Parse the response
      return await response.json() as T;
    } catch (error) {
      console.error(`Error in GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Makes a POST request to the API
   * @param endpoint The API endpoint to call
   * @param data The data to send in the request body
   * @returns The response data
   */
  static async post<T>(endpoint: string, data: any): Promise<T> {
    try {
      // Get the access token
      const token = await AsyncStorage.getItem('auth_token');

      if (!token) {
        throw new Error('No access token available');
      }

      // Make the request
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${errorText}`);
      }

      // Parse the response
      return await response.json() as T;
    } catch (error) {
      console.error(`Error in POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Makes a PUT request to the API
   * @param endpoint The API endpoint to call
   * @param data The data to send in the request body
   * @returns The response data
   */
  static async put<T>(endpoint: string, data: any): Promise<T> {
    try {
      // Get the access token
      const token = await AsyncStorage.getItem('auth_token');

      if (!token) {
        throw new Error('No access token available');
      }

      // Make the request
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${errorText}`);
      }

      // Parse the response
      return await response.json() as T;
    } catch (error) {
      console.error(`Error in PUT request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Makes a DELETE request to the API
   * @param endpoint The API endpoint to call
   * @returns The response data
   */
  static async delete<T>(endpoint: string): Promise<T> {
    try {
      // Get the access token
      const token = await AsyncStorage.getItem('auth_token');

      if (!token) {
        throw new Error('No access token available');
      }

      // Make the request
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${errorText}`);
      }

      // Parse the response
      return await response.json() as T;
    } catch (error) {
      console.error(`Error in DELETE request to ${endpoint}:`, error);
      throw error;
    }
  }
}
