import { getStoredAssistantData } from '@/api/general/assistantApi';
import { Beneficiary, Coordinates, getBeneficiariesByAssistantId, saveBeneficiaryCoordinates } from '@/api/general/beneficiaryApi';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { Alert, RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function BeneficiaryDetailScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isSavingLocation, setIsSavingLocation] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [beneficiary, setBeneficiary] = useState<Beneficiary>({
    id: params.id as string,
    name: params.name as string,
    independent: params.independent as string,
    phoneNumber: params.phoneNumber as string || undefined,
    email: params.email as string || undefined,
  });

  // Parse coordinates if they exist in params
  const coordinatesStr = params.coordinates as string | undefined;
  const [coordinates, setCoordinates] = useState<Coordinates | undefined>(
    coordinatesStr && coordinatesStr !== 'undefined$undefined' ? {
      latitude: parseFloat(coordinatesStr.split('$')[0]),
      longitude: parseFloat(coordinatesStr.split('$')[1])
    } : undefined
  );

  const fetchBeneficiaryData = async () => {
    try {
      const assistantData = await getStoredAssistantData();
      if (!assistantData || !assistantData.id) {
        Alert.alert('Error', 'Assistant data not found. Please log in again.');
        return;
      }

      const beneficiariesData = await getBeneficiariesByAssistantId(assistantData.id);
      const updatedBeneficiary = beneficiariesData.find(b => b.id === beneficiary.id);
      
      if (updatedBeneficiary) {
        setBeneficiary(updatedBeneficiary);
        setCoordinates(updatedBeneficiary.coordinates);
      }
    } catch (error) {
      console.error('Error fetching beneficiary data:', error);
      Alert.alert('Error', 'Failed to refresh beneficiary data. Please try again.');
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchBeneficiaryData();
    setRefreshing(false);
  }, [beneficiary.id]);

  const handleSaveLocation = async () => {
    try {
      setIsSavingLocation(true);

      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Please allow location access to save the beneficiary\'s location.'
        );
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const newCoordinates: Coordinates = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      // Save coordinates to API
      await saveBeneficiaryCoordinates(beneficiary.id, newCoordinates);

      // Fetch fresh data after saving location
      await fetchBeneficiaryData();

      Alert.alert(
        'Success',
        'Location saved successfully!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving location:', error);
      Alert.alert(
        'Error',
        'Failed to save location. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSavingLocation(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#222" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Beneficiary Details</Text>
        <TouchableOpacity>
          <Ionicons name="ellipsis-vertical" size={24} color="#222" />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4AC29A']}
            tintColor="#4AC29A"
          />
        }
      >
        {/* Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileAvatar}>
            <Text style={styles.avatarText}>{beneficiary.name.charAt(0)}</Text>
          </View>
          <Text style={styles.profileName}>{beneficiary.name}</Text>
          <View style={[
            styles.statusBadge,
            beneficiary.independent && styles.statusActive,
            !beneficiary.independent && styles.statusInactive,
          ]}>
            <Text style={styles.statusText}>{beneficiary.independent}</Text>
          </View>
        </View>

        {/* Info Card */}
        <View style={styles.infoCard}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Category:</Text>
            <Text style={styles.infoValue}>{beneficiary.independent}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Phone:</Text>
            <Text style={styles.infoValue}>{beneficiary.phoneNumber || 'Not available'}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Email:</Text>
            <Text style={styles.infoValue}>{beneficiary.email || 'Not available'}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Coordinates:</Text>
            <Text style={styles.infoValue}>
              {coordinates?.latitude !== undefined && coordinates?.longitude !== undefined && coordinates
                ? `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`
                : 'Not available'}
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[!coordinates ? styles.primaryButton : styles.secondaryButton, isSavingLocation && styles.disabledButton]}
            onPress={handleSaveLocation}
            disabled={isSavingLocation}
          >
            <Ionicons name="location-outline" size={20} color={!coordinates ? '#fff' : '#4AC29A'} />
            <Text style={!coordinates ? styles.primaryButtonText : styles.secondaryButtonText}>
              {!coordinates ? (isSavingLocation ? 'Saving Location...' : 'Add current location') : (isSavingLocation ? 'Updating location':'Update current location')}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  scrollView: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4AC29A',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#e0e0e0',
  },
  statusActive: {
    backgroundColor: '#E6F9F2',
  },
  statusInactive: {
    backgroundColor: '#FFE8E8',
  },
  statusPending: {
    backgroundColor: '#FFF8E0',
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#555',
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 15,
    color: '#888',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    maxWidth: '60%',
    textAlign: 'right',
  },
  bioText: {
    fontSize: 15,
    color: '#444',
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 24,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4AC29A',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 8,
  },
  primaryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#4AC29A',
  },
  secondaryButtonText: {
    color: '#4AC29A',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
});
