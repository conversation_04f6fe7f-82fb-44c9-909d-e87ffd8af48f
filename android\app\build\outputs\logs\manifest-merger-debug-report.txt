-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:1:1-37:12
MERGED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:1:1-37:12
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc3d3da355721165959d01988f089f7b\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b10e047db977ffb3716f52022c3b241d\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9915be2300aeb0d47bd05a4e69d44d77\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a9b1d92233988eb6804b4d66f0f0ff2\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3a85ad7036953e5cd0bc7be7a0d86f6\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dcd07d1072d94722748a6e5735bc369\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1632f434b6e58c81f13a97a42c068409\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\800fa29cc7f1db446f4c5dca272b3223\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa181034661dfc41637b95b72189104a\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.linking:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\545455d07264d19f5970651419ca8291\transformed\expo.modules.linking-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:2:1-17:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7951580d1a74b5ae50ca24628a2d517f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb430efb3b96d95de96e855fce06cf16\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\09494fb4bc8f220614094cdf67a9b197\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8f05299ecf36b7f0a10382396160338\transformed\glide-plugin-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c8964cc86241d9329e4cfc32db0733c\transformed\awebp-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11000938d50f861abe720434978a0e8\transformed\apng-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c193342b800a8ea97e3bdf5eaf38463\transformed\gif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44ab755464896b6133708d415e71f91a\transformed\avif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a7daa60ae8e61fb95383ac8acc272d\transformed\frameanimation-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25dd2e9a2ec645a6ceb46052888a9d56\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b45e094077bd90909df4f5fd816fcff1\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22d246360e800147b80b90f315901c5\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acca1163243bab6b1a9afa05ab1c3db\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c8647b949e3960b0e149b81b8e93a7\transformed\avif-integration-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ca716786626fd8445639f8dd7ed357f\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd1680e29a2bb5febce35f72e18cd41\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8315ec2b76c6dfb54a9129d88f0598c1\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\392a1ec38cff39fc5ee9fdcc4d5bf888\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abb036bf02687427f0519cdc07a24b65\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1049192812809e6b77213804b016ec6\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dfd7d573c21c6f1b7b6724f0de9ec1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7daff5b1bab3159e15c22bb2ace0f3ac\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d501250e312faf23654afeb26f10903\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a69babe9d9651d03b9a971a79e86412\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\116c308dcd0af56677669e38e3b2a9e4\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89a14f92f7d6a0d0a0ff24a0417f205f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9291b1c175b553eae62ac701b012f5ed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67554b16c0f66ed5dcd94f202e15e5c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5207d0367f06c1f6e84cbefae3e2b587\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f0b26992784114964f190a561ffe96\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93122185410ef064caadb5d5229c2b16\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31ec4610a53a9488d34d1e7cbe3bcd1f\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26fac8a3640758df51a47894fb0bdf51\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68a88f6140a987db9e1b83ed6e21d374\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f5b354ffa25a5daa0ac494099330e9b\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6437f0af1c2db5610bd35619be9ece2a\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25d0c928bb488ce4e6dba03528eb81\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7361cf75e88c28daea827150262da83c\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f279fa17c01dbfb036d3f1125112bf06\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c58fa155053e6e6b1130141a96e378f\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0a6ca2a1e36d88e3cab7b754a4788a4\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8598b7735b570ffda4ff38358ccfdf3\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5a86f6eff2ecb98629f7bd5ffa37cb8\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b73be68721e121de4bde7537543f6a96\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58841a7ae53777c3220cfc01e3270c2f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27d45eecab5eeeecf670467d9518a2d\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84190173ce0beaa6839fa9d5657e90bb\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce7e675233ed9e1ee4f543b2636541cd\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2b021950bae1ad235c99cc3e937f5e\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f0820ae61e852c4740ea463d137e34\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a81ad93ea84b20ba72a87fafab04254\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9dd9fd341920f52d422b53b122867e\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2728203bb9d13c173abe6fb872cb3a4b\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582d0c2f5e6292ce32dc808d4c3bbb90\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67edbce12d482f99ad850ac6e27ba795\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\379aca9cadd8bb41b8a17f7b6c2df346\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1727a3cb27ebdef3154cd7c2211b8b98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e8e46729d342f606e4e4ba947005117\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a993816cefaaa3391f007cc80d0031d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003b56070dc9f44c660f21a345865b61\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6453ecd10fa736e3e1a54637aa44062a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df43354cc9c88f43706e2de371bba77\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f2e150e406808ecfce394929d0c0fb0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b33af9e005aa4cb2cabc7fb57459d45\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c8c074803c9e9841762deb598f9f542\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\493de7f104d0479b26aa33c67c4bc71a\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f71f88d865b181fe9594c148e82a8e9\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6816887e92705e134b3e1770ca694573\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce5c2e8569e09aff65fdeefb74c6bc71\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a95eb76eac15d8949b90e2b41aa5f8b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faab334db77e08cdd8e99a4aef0ffa3\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852be9e6679aa434c2578b30b83f2ed6\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\da827b85871f4bd0abe0652a3c1ed191\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e295a72abacc6ce20dc0ca41a996c76\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\141de2705609c2aefea70a7a6ec96916\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e53974698227bf58e8a34890a5d6dc1b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5567384c0811be2fdff4aa86a52ddb5b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\645235fd5f58e6da3a574b83261b74f1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbba9395a04335ba141d79ea360d770f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cd6302452352550fbfa35c1ffc800a8\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3a748209e608070e671341ac3c4992a\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12347741fd7aaa625ef78de42c9037c8\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db73666d44fc921249f2da839274405\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\785b9d7ea91b6dca16e15567dd819a36\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b228c99a9f96c9dea6bcb5eea8dce6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec411c7543313074315651000781721\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f209de837fc39a1abddedcfd1cc7b64d\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ecd1c205935d5f68832b0af2e6f1a84\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b7d55a89f62f4e201a645f43e29a02a\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ddc42a24989060bb1d7719a187fa31\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ffb45bef6d4684be778fb0b427ccf0e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9b627952c0ae3220a72a822d081f42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95b7b09e571a173b28e56beed3d6987a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6852474e91b006caf43706869f6c98b1\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6f92620c3c4cda2547a81b6480e4a1b\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5355c705e55ae48e4c42ad662665cc\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0ccd540c127402830e860888bb9bdcf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\34eb33b2db67e77168b1a1fb06221f4c\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4384c858429f01da66b32db7e53f037\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1b839b7e5694a30fdfbb9686960e39\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\454d0c9fa4153227402569024de85159\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a5428e2997af18cb564ece6f864d2d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8050d15875717ad3c035882deb89d68f\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4337afb52a3270499dcf9191ac8fe7ea\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\f222425275c29ab7cab05ecd3f7aef8f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:4:3-64
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:4:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:5:3-77
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:5:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:7:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\800fa29cc7f1db446f4c5dca272b3223\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\800fa29cc7f1db446f4c5dca272b3223\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:8:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:8:20-76
queries
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:9:3-15:13
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:7-58
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:7-67
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:12:17-65
data
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:13:13-35
application
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:3-36:17
MERGED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:3-36:17
MERGED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:3-36:17
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:10:5-15:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7951580d1a74b5ae50ca24628a2d517f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7951580d1a74b5ae50ca24628a2d517f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b45e094077bd90909df4f5fd816fcff1\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b45e094077bd90909df4f5fd816fcff1\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\392a1ec38cff39fc5ee9fdcc4d5bf888\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\392a1ec38cff39fc5ee9fdcc4d5bf888\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abb036bf02687427f0519cdc07a24b65\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abb036bf02687427f0519cdc07a24b65\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1049192812809e6b77213804b016ec6\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1049192812809e6b77213804b016ec6\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7daff5b1bab3159e15c22bb2ace0f3ac\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7daff5b1bab3159e15c22bb2ace0f3ac\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f209de837fc39a1abddedcfd1cc7b64d\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f209de837fc39a1abddedcfd1cc7b64d\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95b7b09e571a173b28e56beed3d6987a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95b7b09e571a173b28e56beed3d6987a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\f222425275c29ab7cab05ecd3f7aef8f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\f222425275c29ab7cab05ecd3f7aef8f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:221-247
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:221-247
	android:label
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:48-80
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:116-161
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:81-115
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:162-188
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:162-188
	android:theme
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:189-220
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:16-47
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:16:16-47
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:5-118
	android:value
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:93-116
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:17:16-92
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:5-135
	android:resource
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:87-133
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:18:16-86
meta-data#expo.modules.notifications.default_notification_icon
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:5-132
	android:resource
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:84-130
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:19:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:5-83
	android:value
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:60-81
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:20:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:5-105
	android:value
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:81-103
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:21:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:5-99
	android:value
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:80-97
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:22:16-79
activity#com.abdoxm.assistantapp.MainActivity
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:5-35:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:167-209
	android:exported
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:44-134
	android:theme
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:210-255
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:23:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:24:7-27:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:25:9-60
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:25:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:26:9-68
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:26:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:assisstantapp+data:scheme:exp+assisstant-app
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:28:7-34:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:9-67
	android:name
		ADDED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\main\AndroidManifest.xml:30:19-65
uses-sdk
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc3d3da355721165959d01988f089f7b\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc3d3da355721165959d01988f089f7b\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b10e047db977ffb3716f52022c3b241d\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b10e047db977ffb3716f52022c3b241d\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9915be2300aeb0d47bd05a4e69d44d77\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9915be2300aeb0d47bd05a4e69d44d77\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a9b1d92233988eb6804b4d66f0f0ff2\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a9b1d92233988eb6804b4d66f0f0ff2\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3a85ad7036953e5cd0bc7be7a0d86f6\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3a85ad7036953e5cd0bc7be7a0d86f6\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dcd07d1072d94722748a6e5735bc369\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dcd07d1072d94722748a6e5735bc369\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1632f434b6e58c81f13a97a42c068409\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1632f434b6e58c81f13a97a42c068409\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\800fa29cc7f1db446f4c5dca272b3223\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\800fa29cc7f1db446f4c5dca272b3223\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa181034661dfc41637b95b72189104a\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa181034661dfc41637b95b72189104a\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\545455d07264d19f5970651419ca8291\transformed\expo.modules.linking-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\545455d07264d19f5970651419ca8291\transformed\expo.modules.linking-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\429888db8e5916503c7ec8b30991ae80\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7951580d1a74b5ae50ca24628a2d517f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7951580d1a74b5ae50ca24628a2d517f\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb430efb3b96d95de96e855fce06cf16\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb430efb3b96d95de96e855fce06cf16\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\09494fb4bc8f220614094cdf67a9b197\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\09494fb4bc8f220614094cdf67a9b197\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8f05299ecf36b7f0a10382396160338\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8f05299ecf36b7f0a10382396160338\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\992ae729079d0d96d05b1ce2a0b0c65e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c8964cc86241d9329e4cfc32db0733c\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c8964cc86241d9329e4cfc32db0733c\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11000938d50f861abe720434978a0e8\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e11000938d50f861abe720434978a0e8\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c193342b800a8ea97e3bdf5eaf38463\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c193342b800a8ea97e3bdf5eaf38463\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44ab755464896b6133708d415e71f91a\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44ab755464896b6133708d415e71f91a\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a7daa60ae8e61fb95383ac8acc272d\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a7daa60ae8e61fb95383ac8acc272d\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25dd2e9a2ec645a6ceb46052888a9d56\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25dd2e9a2ec645a6ceb46052888a9d56\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b45e094077bd90909df4f5fd816fcff1\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b45e094077bd90909df4f5fd816fcff1\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22d246360e800147b80b90f315901c5\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d22d246360e800147b80b90f315901c5\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acca1163243bab6b1a9afa05ab1c3db\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acca1163243bab6b1a9afa05ab1c3db\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c8647b949e3960b0e149b81b8e93a7\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c8647b949e3960b0e149b81b8e93a7\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ca716786626fd8445639f8dd7ed357f\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ca716786626fd8445639f8dd7ed357f\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd1680e29a2bb5febce35f72e18cd41\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd1680e29a2bb5febce35f72e18cd41\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8315ec2b76c6dfb54a9129d88f0598c1\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8315ec2b76c6dfb54a9129d88f0598c1\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\392a1ec38cff39fc5ee9fdcc4d5bf888\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\392a1ec38cff39fc5ee9fdcc4d5bf888\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abb036bf02687427f0519cdc07a24b65\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abb036bf02687427f0519cdc07a24b65\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1049192812809e6b77213804b016ec6\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1049192812809e6b77213804b016ec6\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dfd7d573c21c6f1b7b6724f0de9ec1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dfd7d573c21c6f1b7b6724f0de9ec1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7daff5b1bab3159e15c22bb2ace0f3ac\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7daff5b1bab3159e15c22bb2ace0f3ac\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d501250e312faf23654afeb26f10903\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d501250e312faf23654afeb26f10903\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a69babe9d9651d03b9a971a79e86412\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a69babe9d9651d03b9a971a79e86412\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\116c308dcd0af56677669e38e3b2a9e4\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\116c308dcd0af56677669e38e3b2a9e4\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89a14f92f7d6a0d0a0ff24a0417f205f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\89a14f92f7d6a0d0a0ff24a0417f205f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9291b1c175b553eae62ac701b012f5ed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9291b1c175b553eae62ac701b012f5ed\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67554b16c0f66ed5dcd94f202e15e5c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67554b16c0f66ed5dcd94f202e15e5c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5207d0367f06c1f6e84cbefae3e2b587\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5207d0367f06c1f6e84cbefae3e2b587\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f0b26992784114964f190a561ffe96\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f0b26992784114964f190a561ffe96\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93122185410ef064caadb5d5229c2b16\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93122185410ef064caadb5d5229c2b16\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31ec4610a53a9488d34d1e7cbe3bcd1f\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31ec4610a53a9488d34d1e7cbe3bcd1f\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26fac8a3640758df51a47894fb0bdf51\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26fac8a3640758df51a47894fb0bdf51\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68a88f6140a987db9e1b83ed6e21d374\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68a88f6140a987db9e1b83ed6e21d374\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f5b354ffa25a5daa0ac494099330e9b\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f5b354ffa25a5daa0ac494099330e9b\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6437f0af1c2db5610bd35619be9ece2a\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6437f0af1c2db5610bd35619be9ece2a\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25d0c928bb488ce4e6dba03528eb81\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25d0c928bb488ce4e6dba03528eb81\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7361cf75e88c28daea827150262da83c\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7361cf75e88c28daea827150262da83c\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f279fa17c01dbfb036d3f1125112bf06\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f279fa17c01dbfb036d3f1125112bf06\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c58fa155053e6e6b1130141a96e378f\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c58fa155053e6e6b1130141a96e378f\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0a6ca2a1e36d88e3cab7b754a4788a4\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0a6ca2a1e36d88e3cab7b754a4788a4\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8598b7735b570ffda4ff38358ccfdf3\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8598b7735b570ffda4ff38358ccfdf3\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5a86f6eff2ecb98629f7bd5ffa37cb8\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5a86f6eff2ecb98629f7bd5ffa37cb8\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b73be68721e121de4bde7537543f6a96\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b73be68721e121de4bde7537543f6a96\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58841a7ae53777c3220cfc01e3270c2f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58841a7ae53777c3220cfc01e3270c2f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27d45eecab5eeeecf670467d9518a2d\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27d45eecab5eeeecf670467d9518a2d\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84190173ce0beaa6839fa9d5657e90bb\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84190173ce0beaa6839fa9d5657e90bb\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce7e675233ed9e1ee4f543b2636541cd\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce7e675233ed9e1ee4f543b2636541cd\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2b021950bae1ad235c99cc3e937f5e\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2b021950bae1ad235c99cc3e937f5e\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f0820ae61e852c4740ea463d137e34\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f0820ae61e852c4740ea463d137e34\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a81ad93ea84b20ba72a87fafab04254\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a81ad93ea84b20ba72a87fafab04254\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9dd9fd341920f52d422b53b122867e\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e9dd9fd341920f52d422b53b122867e\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2728203bb9d13c173abe6fb872cb3a4b\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2728203bb9d13c173abe6fb872cb3a4b\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582d0c2f5e6292ce32dc808d4c3bbb90\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582d0c2f5e6292ce32dc808d4c3bbb90\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67edbce12d482f99ad850ac6e27ba795\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\67edbce12d482f99ad850ac6e27ba795\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\379aca9cadd8bb41b8a17f7b6c2df346\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\379aca9cadd8bb41b8a17f7b6c2df346\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1727a3cb27ebdef3154cd7c2211b8b98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1727a3cb27ebdef3154cd7c2211b8b98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e8e46729d342f606e4e4ba947005117\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e8e46729d342f606e4e4ba947005117\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a993816cefaaa3391f007cc80d0031d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a993816cefaaa3391f007cc80d0031d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003b56070dc9f44c660f21a345865b61\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003b56070dc9f44c660f21a345865b61\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6453ecd10fa736e3e1a54637aa44062a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6453ecd10fa736e3e1a54637aa44062a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df43354cc9c88f43706e2de371bba77\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df43354cc9c88f43706e2de371bba77\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f2e150e406808ecfce394929d0c0fb0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f2e150e406808ecfce394929d0c0fb0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b33af9e005aa4cb2cabc7fb57459d45\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b33af9e005aa4cb2cabc7fb57459d45\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c8c074803c9e9841762deb598f9f542\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c8c074803c9e9841762deb598f9f542\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\493de7f104d0479b26aa33c67c4bc71a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\493de7f104d0479b26aa33c67c4bc71a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f71f88d865b181fe9594c148e82a8e9\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f71f88d865b181fe9594c148e82a8e9\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6816887e92705e134b3e1770ca694573\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6816887e92705e134b3e1770ca694573\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce5c2e8569e09aff65fdeefb74c6bc71\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce5c2e8569e09aff65fdeefb74c6bc71\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a95eb76eac15d8949b90e2b41aa5f8b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a95eb76eac15d8949b90e2b41aa5f8b\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faab334db77e08cdd8e99a4aef0ffa3\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1faab334db77e08cdd8e99a4aef0ffa3\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852be9e6679aa434c2578b30b83f2ed6\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852be9e6679aa434c2578b30b83f2ed6\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\da827b85871f4bd0abe0652a3c1ed191\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\da827b85871f4bd0abe0652a3c1ed191\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e295a72abacc6ce20dc0ca41a996c76\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e295a72abacc6ce20dc0ca41a996c76\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\141de2705609c2aefea70a7a6ec96916\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\141de2705609c2aefea70a7a6ec96916\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e53974698227bf58e8a34890a5d6dc1b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e53974698227bf58e8a34890a5d6dc1b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5567384c0811be2fdff4aa86a52ddb5b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5567384c0811be2fdff4aa86a52ddb5b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\645235fd5f58e6da3a574b83261b74f1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\645235fd5f58e6da3a574b83261b74f1\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbba9395a04335ba141d79ea360d770f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbba9395a04335ba141d79ea360d770f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cd6302452352550fbfa35c1ffc800a8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cd6302452352550fbfa35c1ffc800a8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3a748209e608070e671341ac3c4992a\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3a748209e608070e671341ac3c4992a\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12347741fd7aaa625ef78de42c9037c8\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12347741fd7aaa625ef78de42c9037c8\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db73666d44fc921249f2da839274405\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db73666d44fc921249f2da839274405\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\785b9d7ea91b6dca16e15567dd819a36\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\785b9d7ea91b6dca16e15567dd819a36\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b228c99a9f96c9dea6bcb5eea8dce6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b228c99a9f96c9dea6bcb5eea8dce6\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec411c7543313074315651000781721\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec411c7543313074315651000781721\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f209de837fc39a1abddedcfd1cc7b64d\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f209de837fc39a1abddedcfd1cc7b64d\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ecd1c205935d5f68832b0af2e6f1a84\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ecd1c205935d5f68832b0af2e6f1a84\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b7d55a89f62f4e201a645f43e29a02a\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b7d55a89f62f4e201a645f43e29a02a\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ddc42a24989060bb1d7719a187fa31\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ddc42a24989060bb1d7719a187fa31\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ffb45bef6d4684be778fb0b427ccf0e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ffb45bef6d4684be778fb0b427ccf0e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9b627952c0ae3220a72a822d081f42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9b627952c0ae3220a72a822d081f42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95b7b09e571a173b28e56beed3d6987a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95b7b09e571a173b28e56beed3d6987a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6852474e91b006caf43706869f6c98b1\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6852474e91b006caf43706869f6c98b1\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6f92620c3c4cda2547a81b6480e4a1b\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6f92620c3c4cda2547a81b6480e4a1b\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5355c705e55ae48e4c42ad662665cc\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5355c705e55ae48e4c42ad662665cc\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0ccd540c127402830e860888bb9bdcf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0ccd540c127402830e860888bb9bdcf\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\34eb33b2db67e77168b1a1fb06221f4c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\34eb33b2db67e77168b1a1fb06221f4c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4384c858429f01da66b32db7e53f037\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4384c858429f01da66b32db7e53f037\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1b839b7e5694a30fdfbb9686960e39\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1b839b7e5694a30fdfbb9686960e39\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\454d0c9fa4153227402569024de85159\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\454d0c9fa4153227402569024de85159\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a5428e2997af18cb564ece6f864d2d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a5428e2997af18cb564ece6f864d2d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8050d15875717ad3c035882deb89d68f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8050d15875717ad3c035882deb89d68f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4337afb52a3270499dcf9191ac8fe7ea\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4337afb52a3270499dcf9191ac8fe7ea\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\f222425275c29ab7cab05ecd3f7aef8f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\f222425275c29ab7cab05ecd3f7aef8f\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\assistant app\assisstant-app\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\assistant app\assisstant-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
package#host.exp.exponent
ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2621bf82b0b9d39161412f01ca3a3\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8f7dba8018f7d0840d971708dd0e42\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:22-76
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4921e399dc760d526b20c10474ed13ea\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\assistant app\assisstant-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\789b3ca993241fe94861f6e3e6edd8ff\transformed\expo.modules.filesystem-18.1.9\AndroidManifest.xml:22:13-74
service#expo.modules.location.services.LocationTaskService
ADDED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [host.exp.exponent:expo.modules.location:18.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d9bbe866e3bab8e928b4d7f28a1d17c\transformed\expo.modules.location-18.1.4\AndroidManifest.xml:12:13-78
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b17bb88da34f3d70ae40d3a46726b9\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e59da224f64fd3e4fe147fe8a3ccea3b\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6af5baa27348c57aa82393776411b37\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d76105b7a647cc2edc18f5c8ecdb1e99\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\098b84f38fbd7fb7a201fd8477460ff6\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c55f14ee6bfb7ccabedf541c19c7bc6\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6cc7e96abcd1f2665995aea30d859a4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\dac1ac211fbfe78ad3cd929e44306172\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c2eae7aee80e82c79a8ae216e7869c8\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0964178eb307674e8f00b6340d381c7e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbc6c77c646cf846ed804201d2591aa6\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00774c690ca55720da1859b878e48512\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7216252432b2cce12d812bd9b06dfa15\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42554d3c9533484b9bc0eb513e444050\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.abdoxm.assistantapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.abdoxm.assistantapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\480ce62020c830cf93653ad6a17bbf11\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac5a04d60853b9fc4bb91d52842e99f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3273770dd9b216f26223a6f98644967e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\559c92fb3f47cd1fbad469ece3374096\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0297f1c848243ef50e46bd3151056554\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\490fb3dcdd4e0105f0d54840f1711ab0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\95d1294fc77e9bad826f6494d3de3606\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\72a7402d6234c31773e846ec2436a878\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab892e1514d9c5c4a909d4742382db02\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
