#Tue May 13 15:21:45 WEST 2025
path.4=3/classes.dex
path.3=14/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.6=classes3.dex
path.5=classes2.dex
path.0=classes.dex
base.4=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.3=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.2=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.1=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.0=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.6=classes7.dex
renamed.5=classes6.dex
base.6=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
renamed.4=classes5.dex
base.5=C\:\\Users\\Admin\\assistant app\\assisstant-app\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
