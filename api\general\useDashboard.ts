import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCallback, useEffect, useState } from 'react';
import { compareActions, getStoredActions, getUnreadActionsCount, setUnreadActionsCount, storeActions } from './actionsStorage';
import { getStoredAssistantData } from './assistantApi';
import { Action, GeneralStatistics, filterActionsByEmail, getActions, getGeneralStatistics } from './dashboardApi';

/**
 * Hook to get and manage dashboard data
 * @returns An object containing the dashboard data and loading state
 */
export const useDashboard = () => {
  const [statistics, setStatistics] = useState<GeneralStatistics | null>(null);
  const [actions, setActions] = useState<Action[]>([]);
  const [userActions, setUserActions] = useState<Action[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  // Function to check for new actions and update unread count
  const checkForNewActions = useCallback(async (newActions: Action[]) => {
    try {
      // Get stored actions
      const storedActions = await getStoredActions();
      
      // Compare new actions with stored ones
      const newCount = compareActions(newActions, storedActions);
      
      // Update unread count in state and storage
      if (newCount > 0) {
        setUnreadCount(newCount);
        await setUnreadActionsCount(newCount);
      } else {
        // Keep the existing unread count if no new actions
        const currentCount = await getUnreadActionsCount();
        setUnreadCount(currentCount);
      }
    } catch (err) {
      console.error('Error checking for new actions:', err);
    }
  }, []);

  // Function to mark all actions as read
  const markActionsAsRead = useCallback(async () => {
    try {
      // Store current actions
      await storeActions(userActions);
      // Reset unread count
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking actions as read:', err);
    }
  }, [userActions]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch general statistics
        const statsData = await getGeneralStatistics();
        setStatistics(statsData);

        // Fetch actions
        const actionsData = await getActions(0);
        const actionsList = actionsData.content || [];
        setActions(actionsList);

        // Try to get assistant data first (which has the email)
        const assistantData = await getStoredAssistantData();
        if (assistantData && assistantData.email) {
          const filteredActions = filterActionsByEmail(actionsList, assistantData.email);
          setUserActions(filteredActions);
          
          // Check for new actions
          await checkForNewActions(filteredActions);
        } else {
          // Fallback to user data from storage
          const userData = await AsyncStorage.getItem('user_data');
          if (userData) {
            const user = JSON.parse(userData);

            // Try to get email from different possible locations
            const userEmail = user.email || user.userPrincipalName || (user.account && user.account.username);

            if (userEmail) {
              // Filter actions by user email
              const filteredActions = filterActionsByEmail(actionsList, userEmail);
              setUserActions(filteredActions);
              
              // Check for new actions
              await checkForNewActions(filteredActions);
            } else {
              console.warn('No email found in user data');
              setUserActions([]);
            }
          } else {
            console.warn('No user data found in storage');
            setUserActions([]);
          }
        }

        setError(null);
      } catch (err) {
        console.error('Error in useDashboard hook:', err);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [checkForNewActions]);

  /**
   * Refreshes the dashboard data
   */
  const refreshDashboardData = async () => {
    try {
      setLoading(false);

      // Fetch general statistics
      const statsData = await getGeneralStatistics();
      setStatistics(statsData);

      // Fetch actions
      const actionsData = await getActions(0);
      const actionsList = actionsData.content || [];
      setActions(actionsList);

      // Try to get assistant data first (which has the email)
      const assistantData = await getStoredAssistantData();
      if (assistantData && assistantData.email) {
        const filteredActions = filterActionsByEmail(actionsData.content, assistantData.email);
        setUserActions(filteredActions);
        
        // Check for new actions
        await checkForNewActions(filteredActions);
      } else {
        // Fallback to user data from storage
        const userData = await AsyncStorage.getItem('user_data');
        if (userData) {
          const user = JSON.parse(userData);
          console.log('User data from storage (refresh):', user);

          // Try to get email from different possible locations
          const userEmail = user.email || user.userPrincipalName || (user.account && user.account.username);

          if (userEmail) {
            console.log('Using user email for filtering actions (refresh):', userEmail);
            // Filter actions by user email
            const filteredActions = filterActionsByEmail(actionsData.content, userEmail);
            setUserActions(filteredActions);
            
            // Check for new actions
            await checkForNewActions(filteredActions);
          } else {
            console.warn('No email found in user data (refresh)');
            setUserActions([]);
          }
        } else {
          console.warn('No user data found in storage (refresh)');
          setUserActions([]);
        }
      }

      setError(null);
      return { statistics: statsData, actions: actionsData.content };
    } catch (err) {
      console.error('Error refreshing dashboard data:', err);
      setError('Failed to refresh dashboard data');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    statistics,
    actions,
    userActions,
    userActionsCount: unreadCount,
    loading,
    error,
    refreshDashboardData,
    markActionsAsRead,
  };
};
